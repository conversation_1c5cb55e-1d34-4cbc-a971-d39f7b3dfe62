<?php
/*
 * Test file for QR Code Integration with WCDP
 * This file can be used to test the QR code functionality
 * 
 * Usage: Add ?test_qr_integration=1 to any WordPress admin page URL
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only run test if requested and user has admin privileges
if (isset($_GET['test_qr_integration']) && $_GET['test_qr_integration'] === '1' && current_user_can('manage_options')) {
    add_action('admin_init', 'test_qr_code_integration');
}

function test_qr_code_integration() {
    echo '<div style="padding: 20px; background: #fff; margin: 20px;">';
    echo '<h2>QR Code Integration Test</h2>';
    
    // Test 1: Check if custom QR plugin function exists
    echo '<h3>Test 1: Plugin Function Check</h3>';
    if (function_exists('generate_custom_qr_code')) {
        echo '<p style="color: green;">✓ generate_custom_qr_code function exists</p>';
        
        // Test 2: Test API call
        echo '<h3>Test 2: API Connection Test</h3>';
        $test_result = generate_custom_qr_code('Test QR Code', array('ajax_request' => true));
        
        if (is_array($test_result) && isset($test_result['success'])) {
            if ($test_result['success']) {
                echo '<p style="color: green;">✓ QR Code API is working correctly</p>';
                echo '<p>Generated SVG length: ' . strlen($test_result['svg']) . ' characters</p>';
            } else {
                echo '<p style="color: red;">✗ QR Code API Error: ' . $test_result['error'] . '</p>';
            }
        } else {
            echo '<p style="color: orange;">⚠ Unexpected response format</p>';
        }
    } else {
        echo '<p style="color: red;">✗ generate_custom_qr_code function not found</p>';
    }
    
    // Test 3: Check WCDP integration
    echo '<h3>Test 3: WCDP Integration Check</h3>';
    if (function_exists('wcdp_check_mode_design_page')) {
        echo '<p style="color: green;">✓ WCDP functions are available</p>';
    } else {
        echo '<p style="color: red;">✗ WCDP functions not found - make sure WCDP is active</p>';
    }
    
    // Test 4: Check AJAX handlers
    echo '<h3>Test 4: AJAX Handler Check</h3>';
    if (has_action('wp_ajax_wcdp_custom_qr_code')) {
        echo '<p style="color: green;">✓ AJAX handler for logged-in users is registered</p>';
    } else {
        echo '<p style="color: red;">✗ AJAX handler for logged-in users not found</p>';
    }
    
    if (has_action('wp_ajax_nopriv_wcdp_custom_qr_code')) {
        echo '<p style="color: green;">✓ AJAX handler for non-logged-in users is registered</p>';
    } else {
        echo '<p style="color: red;">✗ AJAX handler for non-logged-in users not found</p>';
    }
    
    // Test 5: JavaScript integration check
    echo '<h3>Test 5: JavaScript Integration</h3>';
    echo '<p>Check browser console for wcdp_qr_ajax object when on WCDP editor page</p>';
    echo '<script>
        if (typeof wcdp_qr_ajax !== "undefined") {
            console.log("✓ wcdp_qr_ajax object is available:", wcdp_qr_ajax);
        } else {
            console.log("✗ wcdp_qr_ajax object not found");
        }
    </script>';
    
    echo '<h3>Integration Summary</h3>';
    echo '<p>If all tests pass, the QR code integration should work correctly in the WCDP editor.</p>';
    echo '<p><strong>Next steps:</strong></p>';
    echo '<ul>';
    echo '<li>Open a WCDP editor page</li>';
    echo '<li>Click on the QR code tab</li>';
    echo '<li>Enter some text and click "Generate QR code"</li>';
    echo '<li>Check that a QR code with logo is generated</li>';
    echo '</ul>';
    
    echo '</div>';
    wp_die();
}
