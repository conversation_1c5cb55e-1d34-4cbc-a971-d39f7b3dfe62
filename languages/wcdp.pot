#, fuzzy
msgid ""
msgstr ""
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"Project-Id-Version: Woocommerce Designer Pro\n"
"POT-Creation-Date: 2021-02-21 06:18+0100\n"
"PO-Revision-Date: 2018-02-09 20:11+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.0.1\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: wc-designer-pro.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: includes/updaters/wcdp-updater.php:30
msgid "Downloading package from envato market..."
msgstr ""

#: includes/updaters/wcdp-updater.php:35
msgid "Error! Can't connect to filesystem"
msgstr ""

#: includes/updaters/wcdp-updater.php:43
msgid ""
"To receive automatic updates license activation is required. Please visit <a "
"href=\""
msgstr ""

#: includes/updaters/wcdp-updater.php:56
msgid "Error! Envato API error"
msgstr ""

#: includes/updaters/wcdp-updater.php:74
msgid "Error copy package"
msgstr ""

#: includes/updaters/wcdp-updating-manager.php:135
msgid "Download new version from CodeCanyon."
msgstr ""

#: includes/updaters/wcdp-updating-manager.php:137
msgid "Update WooCommerce Designer Pro now."
msgstr ""

#: includes/wcdp-admin-menus.php:5
msgid "Settings WC Designer Pro"
msgstr ""

#: includes/wcdp-admin-menus.php:13 includes/wcdp-admin-menus.php:14
msgid "Settings"
msgstr ""

#: includes/wcdp-admin-menus.php:20 includes/wcdp-admin-menus.php:21
msgid "Fonts"
msgstr ""

#: includes/wcdp-admin-menus.php:27 includes/wcdp-admin-menus.php:28
#: includes/wcdp-admin-menus.php:804 includes/wcdp-content-editor.php:53
msgid "Shapes"
msgstr ""

#: includes/wcdp-admin-menus.php:34 includes/wcdp-admin-menus.php:35
#: includes/wcdp-content-editor.php:54 includes/wcdp-metabox-cliparts.php:6
#: includes/wcdp-metabox-cliparts.php:8 includes/wcdp-metabox-cliparts.php:76
msgid "Cliparts"
msgstr ""

#: includes/wcdp-admin-menus.php:41 includes/wcdp-admin-menus.php:42
#: includes/wcdp-content-editor.php:57 includes/wcdp-metabox-calendars.php:6
#: includes/wcdp-metabox-calendars.php:8 includes/wcdp-metabox-calendars.php:76
msgid "Calendars"
msgstr ""

#: includes/wcdp-admin-menus.php:48 includes/wcdp-admin-menus.php:49
msgid "Image filters"
msgstr ""

#: includes/wcdp-admin-menus.php:55 includes/wcdp-admin-menus.php:56
#: includes/wcdp-metabox-params.php:6 includes/wcdp-metabox-params.php:8
msgid "Parameters"
msgstr ""

#: includes/wcdp-admin-menus.php:62 includes/wcdp-admin-menus.php:63
#: includes/wcdp-metabox-designs.php:44
msgid "Categories"
msgstr ""

#: includes/wcdp-admin-menus.php:69 includes/wcdp-admin-menus.php:70
#: includes/wcdp-metabox-designs.php:6 includes/wcdp-metabox-designs.php:8
msgid "Designs"
msgstr ""

#: includes/wcdp-admin-menus.php:76 includes/wcdp-admin-menus.php:77
msgid "Documentation"
msgstr ""

#: includes/wcdp-admin-menus.php:91
msgid "WooCommerce Designer Pro Settings"
msgstr ""

#: includes/wcdp-admin-menus.php:97
msgid "General"
msgstr ""

#: includes/wcdp-admin-menus.php:98
msgid "Shortcut Keys"
msgstr ""

#: includes/wcdp-admin-menus.php:99
msgid "Style"
msgstr ""

#: includes/wcdp-admin-menus.php:100 includes/wcdp-admin-menus.php:141
msgid "License"
msgstr ""

#: includes/wcdp-admin-menus.php:138
msgid "General settings"
msgstr ""

#: includes/wcdp-admin-menus.php:139
msgid "Define Shortcut Keys"
msgstr ""

#: includes/wcdp-admin-menus.php:140
msgid "Style options"
msgstr ""

#: includes/wcdp-admin-menus.php:153
msgid "Default page for editor:"
msgstr ""

#: includes/wcdp-admin-menus.php:154 includes/wcdp-metabox-params.php:214
msgid "Select a page where the designs will be personalized."
msgstr ""

#: includes/wcdp-admin-menus.php:159
msgid "Add shortcode manually for editor page:"
msgstr ""

#: includes/wcdp-admin-menus.php:160
msgid "Copy shortcode: [wcdp_editor], on the default page for editor."
msgstr ""

#: includes/wcdp-admin-menus.php:165
msgid "Default page for my designs:"
msgstr ""

#: includes/wcdp-admin-menus.php:166
msgid "Select a page where the saved designs of the user will be displayed."
msgstr ""

#: includes/wcdp-admin-menus.php:171
msgid "Add shortcode manually for my designs page:"
msgstr ""

#: includes/wcdp-admin-menus.php:172
msgid "Copy shortcode: [wcdp_my_designs], on the default page for my designs."
msgstr ""

#: includes/wcdp-admin-menus.php:177
msgid "Add buttom my designs for account page:"
msgstr ""

#: includes/wcdp-admin-menus.php:178
msgid "Requires to have selected a my designs page for link."
msgstr ""

#: includes/wcdp-admin-menus.php:183
msgid "Enable RTL mode:"
msgstr ""

#: includes/wcdp-admin-menus.php:184
msgid "Enable right to left reading mode."
msgstr ""

#: includes/wcdp-admin-menus.php:189
msgid "Enable CMYK editor (Requires ImageMagick):"
msgstr ""

#: includes/wcdp-admin-menus.php:195
msgid "Conversion ImageMagick by shell commands:"
msgstr ""

#: includes/wcdp-admin-menus.php:196
msgid ""
"Change all conversions to CMYK by shell commands.<br><b>Important!!</b> The "
"use of commands will have a higher overhead and the processes tend to be "
"slower depending on your server."
msgstr ""

#: includes/wcdp-admin-menus.php:201
msgid "Enable CMYK in color picker:"
msgstr ""

#: includes/wcdp-admin-menus.php:202
msgid ""
"Show and convert the color picker in CMYK automatically. Requires option "
"CMYK enabled."
msgstr ""

#: includes/wcdp-admin-menus.php:206
msgid "Update table"
msgstr ""

#: includes/wcdp-admin-menus.php:208
msgid "Update colors table in color picker:"
msgstr ""

#: includes/wcdp-admin-menus.php:209
msgid ""
"If you change the default profiles, you can make a new CMYK color table with "
"the new selected profiles and update the color picker. Requires option CMYK "
"enabled."
msgstr ""

#: includes/wcdp-admin-menus.php:214
msgid "Max colors chunck by conversion to CMYK:"
msgstr ""

#: includes/wcdp-admin-menus.php:215
msgid ""
"Select the maximum number of colors per conversion at a time to make the "
"color picker table and to convert the SVG images uploads into CMYK. You can "
"increase the value depending on the speed and memory of your server."
msgstr ""

#: includes/wcdp-admin-menus.php:217
msgid "1 color"
msgstr ""

#: includes/wcdp-admin-menus.php:218
msgid "4 colors"
msgstr ""

#: includes/wcdp-admin-menus.php:219
msgid "8 colors"
msgstr ""

#: includes/wcdp-admin-menus.php:220
msgid "16 colors"
msgstr ""

#: includes/wcdp-admin-menus.php:221
msgid "32 colors"
msgstr ""

#: includes/wcdp-admin-menus.php:222
msgid "64 colors"
msgstr ""

#: includes/wcdp-admin-menus.php:223
msgid "128 colors"
msgstr ""

#: includes/wcdp-admin-menus.php:224
msgid "256 colors"
msgstr ""

#: includes/wcdp-admin-menus.php:225
msgid "512 colors"
msgstr ""

#: includes/wcdp-admin-menus.php:231
msgid "Convert images uploads to CMYK automatically:"
msgstr ""

#: includes/wcdp-admin-menus.php:232
msgid "Uploads in editor, cliparts, calendars. Requires option CMYK enabled."
msgstr ""

#: includes/wcdp-admin-menus.php:238 includes/wcdp-admin-menus.php:246
msgid "profile"
msgstr ""

#: includes/wcdp-admin-menus.php:239
msgid "Select RGB profile:"
msgstr ""

#: includes/wcdp-admin-menus.php:240 includes/wcdp-admin-menus.php:248
msgid "Default profile"
msgstr ""

#: includes/wcdp-admin-menus.php:247
msgid "Select CMYK profile:"
msgstr ""

#: includes/wcdp-admin-menus.php:253
msgid "Zip output name:"
msgstr ""

#: includes/wcdp-admin-menus.php:254
msgid "Zip output name."
msgstr ""

#: includes/wcdp-admin-menus.php:259
msgid "Output SVG:"
msgstr ""

#: includes/wcdp-admin-menus.php:260
msgid "Include the SVG design in the output."
msgstr ""

#: includes/wcdp-admin-menus.php:265
msgid "Add SVG to the user downloads:"
msgstr ""

#: includes/wcdp-admin-menus.php:266
msgid ""
"Include the SVG design for user downloads. Requires option Output SVG "
"enabled."
msgstr ""

#: includes/wcdp-admin-menus.php:271
msgid "Output PDF:"
msgstr ""

#: includes/wcdp-admin-menus.php:272
msgid ""
"Include the PDF design in the output. You can configure the size in the "
"parameters."
msgstr ""

#: includes/wcdp-admin-menus.php:277
msgid "Add PDF to the user downloads:"
msgstr ""

#: includes/wcdp-admin-menus.php:278
msgid ""
"Include the PDF design for user downloads. Requires option Output PDF "
"enabled."
msgstr ""

#: includes/wcdp-admin-menus.php:283
msgid "Output PDF with SVG:"
msgstr ""

#: includes/wcdp-admin-menus.php:284
msgid ""
"Include the SVG design in the output PDF. Requires option Output PDF enabled."
msgstr ""

#: includes/wcdp-admin-menus.php:289
msgid "Output PNG:"
msgstr ""

#: includes/wcdp-admin-menus.php:290
msgid "Include the PNG design in the output."
msgstr ""

#: includes/wcdp-admin-menus.php:295
msgid "Add PNG to the user downloads:"
msgstr ""

#: includes/wcdp-admin-menus.php:296
msgid ""
"Include the PNG design for user downloads. Requires option Output PNG "
"enabled."
msgstr ""

#: includes/wcdp-admin-menus.php:301
msgid "Output CMYK:"
msgstr ""

#: includes/wcdp-admin-menus.php:302
msgid ""
"Includes the CMYK design in the output for the admin. Requires option CMYK "
"enabled."
msgstr ""

#: includes/wcdp-admin-menus.php:307
msgid "Output CMYK to the user:"
msgstr ""

#: includes/wcdp-admin-menus.php:308
msgid ""
"Includes the CMYK design in the output for the user. Requires option Output "
"CMYK enabled."
msgstr ""

#: includes/wcdp-admin-menus.php:313
msgid "Output JSON:"
msgstr ""

#: includes/wcdp-admin-menus.php:314
msgid "Includes the JSON design in the output for the admin."
msgstr ""

#: includes/wcdp-admin-menus.php:319
msgid "Enable SVG image uploads to the user:"
msgstr ""

#: includes/wcdp-admin-menus.php:320
msgid ""
"It allows users to upload SVG images in the editor. This option is disabled "
"by default, for security reasons."
msgstr ""

#: includes/wcdp-admin-menus.php:325 includes/wcdp-admin-menus.php:332
#: includes/wcdp-admin-menus.php:339 includes/wcdp-admin-menus.php:346
#: includes/wcdp-admin-menus.php:353
msgid "Get a key"
msgstr ""

#: includes/wcdp-admin-menus.php:326
msgid "Static maps API:"
msgstr ""

#: includes/wcdp-admin-menus.php:327
msgid "Add required API key for the static maps section."
msgstr ""

#: includes/wcdp-admin-menus.php:333
msgid "Pixabay API:"
msgstr ""

#: includes/wcdp-admin-menus.php:334
msgid "Add the API key required for the image search in Pixabay."
msgstr ""

#: includes/wcdp-admin-menus.php:340
msgid "Unsplash API:"
msgstr ""

#: includes/wcdp-admin-menus.php:341
msgid "Add the API key required for the image search in Unsplash."
msgstr ""

#: includes/wcdp-admin-menus.php:347
msgid "Pexels API:"
msgstr ""

#: includes/wcdp-admin-menus.php:348
msgid "Add the API key required for the image search in Pexels."
msgstr ""

#: includes/wcdp-admin-menus.php:354
msgid "Flaticon API:"
msgstr ""

#: includes/wcdp-admin-menus.php:355
msgid "Add the API key required for the image search in Flaticon."
msgstr ""

#: includes/wcdp-admin-menus.php:360
msgid "Add Flaticon icons in SVG:"
msgstr ""

#: includes/wcdp-admin-menus.php:361
msgid "Add Flaticon icons to the canvas in SVG format."
msgstr ""

#: includes/wcdp-admin-menus.php:366
msgid "Convert resources in CMYK:"
msgstr ""

#: includes/wcdp-admin-menus.php:367
msgid ""
"Convert images of Pixabay, Unsplash and Pexels in CMYK before adding it to "
"the canvas. Requires option CMYK enabled."
msgstr ""

#: includes/wcdp-admin-menus.php:372
msgid "Preload fonts unicode:"
msgstr ""

#: includes/wcdp-admin-menus.php:373
msgid ""
"Select an alphabet to preload it in fonts. This option is only necessary "
"when working with fonts in the selected language."
msgstr ""

#: includes/wcdp-admin-menus.php:375 includes/wcdp-content-editor.php:106
#: includes/wcdp-content-editor.php:110 includes/wcdp-content-editor.php:208
#: includes/wcdp-content-editor.php:225 includes/wcdp-manage-filters.php:41
msgid "None"
msgstr ""

#: includes/wcdp-admin-menus.php:376
msgid "Arabic alphabet"
msgstr ""

#: includes/wcdp-admin-menus.php:377
msgid "Latin extended alphabet"
msgstr ""

#: includes/wcdp-admin-menus.php:384 includes/wcdp-metabox-params.php:266
msgid "image"
msgstr ""

#: includes/wcdp-admin-menus.php:385
msgid "Add loader image:"
msgstr ""

#: includes/wcdp-admin-menus.php:386
msgid "Image that reports when the editor is loading or an AJAX query."
msgstr ""

#: includes/wcdp-admin-menus.php:391
msgid "Loader image width:"
msgstr ""

#: includes/wcdp-admin-menus.php:393
msgid "Set loader image width."
msgstr ""

#: includes/wcdp-admin-menus.php:398
msgid "Loader image height:"
msgstr ""

#: includes/wcdp-admin-menus.php:400
msgid "Set loader image height."
msgstr ""

#: includes/wcdp-admin-menus.php:405
msgid "Hide add to cart button in the editor:"
msgstr ""

#: includes/wcdp-admin-menus.php:406
msgid "Hide the add to cart button and the product options tab in the editor."
msgstr ""

#: includes/wcdp-admin-menus.php:411
msgid "Hide add to cart button for customized products:"
msgstr ""

#: includes/wcdp-admin-menus.php:412
msgid ""
"Hide the add to cart button for customizable products in the product page."
msgstr ""

#: includes/wcdp-admin-menus.php:417
msgid "Number of designs the user can save:"
msgstr ""

#: includes/wcdp-admin-menus.php:419
msgid ""
"Number of designs the user can save in the editor. Default empty can save "
"unlimited designs."
msgstr ""

#: includes/wcdp-admin-menus.php:424
msgid "Download design from the editor:"
msgstr ""

#: includes/wcdp-admin-menus.php:425
msgid "Allows users to download the design from the editor."
msgstr ""

#: includes/wcdp-admin-menus.php:430
msgid "Download design from shopping cart:"
msgstr ""

#: includes/wcdp-admin-menus.php:431
msgid "Allows users to download the design from the shopping cart."
msgstr ""

#: includes/wcdp-admin-menus.php:436
msgid "Download design only to user logged:"
msgstr ""

#: includes/wcdp-admin-menus.php:437
msgid ""
"Allow the download of design from the editor and the shopping cart only to "
"user logged."
msgstr ""

#: includes/wcdp-admin-menus.php:442
msgid "Download design in the order"
msgstr ""

#: includes/wcdp-admin-menus.php:443
msgid "Allows users to download the design in the order."
msgstr ""

#: includes/wcdp-admin-menus.php:448
msgid "Add link to email for download the design after finishing the order:"
msgstr ""

#: includes/wcdp-admin-menus.php:449
msgid "Allows the user to download the design from the order email."
msgstr ""

#: includes/wcdp-admin-menus.php:454
msgid ""
"Add link to email for download the design after change in the order status "
"to completed:"
msgstr ""

#: includes/wcdp-admin-menus.php:455
msgid ""
"Allows the user to download the design from the order email when changing "
"the status to completed."
msgstr ""

#: includes/wcdp-admin-menus.php:460
msgid "Add confirmation box to review design:"
msgstr ""

#: includes/wcdp-admin-menus.php:461
msgid "Confirmation required to review the design before adding to cart."
msgstr ""

#: includes/wcdp-admin-menus.php:466
msgid "Text for confirmation box:"
msgstr ""

#: includes/wcdp-admin-menus.php:467
msgid ""
"Example: Please review your design carefully. Check that the design does not "
"contain spelling errors, etc."
msgstr ""

#: includes/wcdp-admin-menus.php:472
msgid "Label for confirmation box:"
msgstr ""

#: includes/wcdp-admin-menus.php:473
msgid "Example: I have reviewed the design and I give my confirmation."
msgstr ""

#: includes/wcdp-admin-menus.php:477
msgid "Restore defaults"
msgstr ""

#: includes/wcdp-admin-menus.php:479
msgid "Restore all defaults settings:"
msgstr ""

#: includes/wcdp-admin-menus.php:480
msgid "Careful this option will erase all your saved settings."
msgstr ""

#: includes/wcdp-admin-menus.php:513
msgid "Move objects up."
msgstr ""

#: includes/wcdp-admin-menus.php:518
msgid "Move objects down."
msgstr ""

#: includes/wcdp-admin-menus.php:523
msgid "Move objects left."
msgstr ""

#: includes/wcdp-admin-menus.php:528
msgid "Move objects right."
msgstr ""

#: includes/wcdp-admin-menus.php:533
msgid "Select all objects."
msgstr ""

#: includes/wcdp-admin-menus.php:538
msgid "Erase all objects."
msgstr ""

#: includes/wcdp-admin-menus.php:543
msgid "Show/Hide grid."
msgstr ""

#: includes/wcdp-admin-menus.php:548
msgid "Center objects vertically."
msgstr ""

#: includes/wcdp-admin-menus.php:553
msgid "Center objects horizontally."
msgstr ""

#: includes/wcdp-admin-menus.php:558
msgid "Flip objects vertically."
msgstr ""

#: includes/wcdp-admin-menus.php:563
msgid "Flip objects horizontally."
msgstr ""

#: includes/wcdp-admin-menus.php:568
msgid "Bring object to front."
msgstr ""

#: includes/wcdp-admin-menus.php:573
msgid "Bring object to back."
msgstr ""

#: includes/wcdp-admin-menus.php:578
msgid "Lock and unlock object."
msgstr ""

#: includes/wcdp-admin-menus.php:583
msgid "Duplicate objects."
msgstr ""

#: includes/wcdp-admin-menus.php:588
msgid ""
"Duplicate objects from the front side to the back side. If there is only one "
"side it will be deactivated."
msgstr ""

#: includes/wcdp-admin-menus.php:593
msgid "Align selected objects vertically with more space between them."
msgstr ""

#: includes/wcdp-admin-menus.php:598
msgid "Align selected objects vertically with less space between them."
msgstr ""

#: includes/wcdp-admin-menus.php:603
msgid "Align objects to left."
msgstr ""

#: includes/wcdp-admin-menus.php:608
msgid "Align objects to right."
msgstr ""

#: includes/wcdp-admin-menus.php:613
msgid "Rotate objects."
msgstr ""

#: includes/wcdp-admin-menus.php:618
msgid "Returns the objects to their original state."
msgstr ""

#: includes/wcdp-admin-menus.php:623
msgid "Align selected objects vertically between them."
msgstr ""

#: includes/wcdp-admin-menus.php:628
msgid "Align selected objects horizontally between them."
msgstr ""

#: includes/wcdp-admin-menus.php:633
msgid "Group and ungroup objects."
msgstr ""

#: includes/wcdp-admin-menus.php:638
msgid "Delete objects."
msgstr ""

#: includes/wcdp-admin-menus.php:643
msgid "Undo changes."
msgstr ""

#: includes/wcdp-admin-menus.php:648
msgid "Redo changes."
msgstr ""

#: includes/wcdp-admin-menus.php:653
msgid "Height of the smallest text line."
msgstr ""

#: includes/wcdp-admin-menus.php:658
msgid "Height of the larger text line."
msgstr ""

#: includes/wcdp-admin-menus.php:683
msgid "Skin predesigned colors:"
msgstr ""

#: includes/wcdp-admin-menus.php:684
msgid "Select a option and save the changes, to apply the predesigned colors."
msgstr ""

#: includes/wcdp-admin-menus.php:686
msgid "Default"
msgstr ""

#: includes/wcdp-admin-menus.php:687
msgid "Gray & Blue"
msgstr ""

#: includes/wcdp-admin-menus.php:688
msgid "Green & Coral"
msgstr ""

#: includes/wcdp-admin-menus.php:689
msgid "Blue & Orange"
msgstr ""

#: includes/wcdp-admin-menus.php:690
msgid "Violet & Dark blue"
msgstr ""

#: includes/wcdp-admin-menus.php:691
msgid "Black & Red"
msgstr ""

#: includes/wcdp-admin-menus.php:698
msgid "Icons color:"
msgstr ""

#: includes/wcdp-admin-menus.php:700
msgid "Icons"
msgstr ""

#: includes/wcdp-admin-menus.php:701
msgid "Icons hover"
msgstr ""

#: includes/wcdp-admin-menus.php:702
msgid "Icons BG"
msgstr ""

#: includes/wcdp-admin-menus.php:703
msgid "Icons BG hover"
msgstr ""

#: includes/wcdp-admin-menus.php:709
msgid "Buttons color:"
msgstr ""

#: includes/wcdp-admin-menus.php:711 includes/wcdp-admin-menus.php:722
msgid "Buttons"
msgstr ""

#: includes/wcdp-admin-menus.php:712 includes/wcdp-admin-menus.php:723
msgid "Buttons hover"
msgstr ""

#: includes/wcdp-admin-menus.php:713 includes/wcdp-admin-menus.php:724
#: includes/wcdp-admin-menus.php:735
msgid "Buttons BG"
msgstr ""

#: includes/wcdp-admin-menus.php:714 includes/wcdp-admin-menus.php:725
msgid "Buttons BG hover"
msgstr ""

#: includes/wcdp-admin-menus.php:720
msgid "Buttons color box messages:"
msgstr ""

#: includes/wcdp-admin-menus.php:731
msgid "Buttons color folders:"
msgstr ""

#: includes/wcdp-admin-menus.php:733
msgid "Folders"
msgstr ""

#: includes/wcdp-admin-menus.php:734
msgid "Folders select"
msgstr ""

#: includes/wcdp-admin-menus.php:736
msgid "Buttons BG select"
msgstr ""

#: includes/wcdp-admin-menus.php:742
msgid "Tabs color:"
msgstr ""

#: includes/wcdp-admin-menus.php:744
msgid "Tabs Text"
msgstr ""

#: includes/wcdp-admin-menus.php:745
msgid "Tabs BG"
msgstr ""

#: includes/wcdp-admin-menus.php:746
msgid "Tabs Content"
msgstr ""

#: includes/wcdp-admin-menus.php:752
msgid "Tooltip color:"
msgstr ""

#: includes/wcdp-admin-menus.php:754
msgid "Tooltip"
msgstr ""

#: includes/wcdp-admin-menus.php:755
msgid "Tooltip BG"
msgstr ""

#: includes/wcdp-admin-menus.php:761
msgid "Scroll bar color:"
msgstr ""

#: includes/wcdp-admin-menus.php:763 includes/wcdp-admin-menus.php:792
msgid "Background"
msgstr ""

#: includes/wcdp-admin-menus.php:769
msgid "Tooltip offset x:"
msgstr ""

#: includes/wcdp-admin-menus.php:770
msgid "Tooltip position horizontally. Default 0."
msgstr ""

#: includes/wcdp-admin-menus.php:775
msgid "Tooltip offset y:"
msgstr ""

#: includes/wcdp-admin-menus.php:776
msgid "Tooltip position vertically. Default -5."
msgstr ""

#: includes/wcdp-admin-menus.php:781
msgid "Border colors:"
msgstr ""

#: includes/wcdp-admin-menus.php:783
msgid "Skin"
msgstr ""

#: includes/wcdp-admin-menus.php:784
msgid "Bleed area"
msgstr ""

#: includes/wcdp-admin-menus.php:790
msgid "Color picker colors:"
msgstr ""

#: includes/wcdp-admin-menus.php:793 includes/wcdp-content-editor.php:207
msgid "Border"
msgstr ""

#: includes/wcdp-admin-menus.php:794 includes/wcdp-admin-menus.php:802
#: includes/wcdp-content-editor.php:51 includes/wcdp-translations.php:28
msgid "Text"
msgstr ""

#: includes/wcdp-admin-menus.php:800
msgid "Colors default in editor:"
msgstr ""

#: includes/wcdp-admin-menus.php:803
msgid "Text outline"
msgstr ""

#: includes/wcdp-admin-menus.php:805
msgid "Shapes outline"
msgstr ""

#: includes/wcdp-admin-menus.php:806
msgid "QR"
msgstr ""

#: includes/wcdp-admin-menus.php:807
msgid "QR BG"
msgstr ""

#: includes/wcdp-admin-menus.php:808
msgid "Map icon"
msgstr ""

#: includes/wcdp-admin-menus.php:814
msgid "Automatic bleed area color:"
msgstr ""

#: includes/wcdp-admin-menus.php:815
msgid ""
"Enable / Disable display color of the light bleed area on dark backgrounds."
msgstr ""

#: includes/wcdp-admin-menus.php:820
msgid "Auto snap:"
msgstr ""

#: includes/wcdp-admin-menus.php:821
msgid "Enable / Disable auto snap mode."
msgstr ""

#: includes/wcdp-admin-menus.php:826
msgid "Auto snap tolerance:"
msgstr ""

#: includes/wcdp-admin-menus.php:827
msgid "Tolerance when lock the objects with the auto snap. Default 5."
msgstr ""

#: includes/wcdp-admin-menus.php:832
msgid "Auto snap color:"
msgstr ""

#: includes/wcdp-admin-menus.php:834
msgid "Guides"
msgstr ""

#: includes/wcdp-admin-menus.php:840
msgid "Centered scaling:"
msgstr ""

#: includes/wcdp-admin-menus.php:841
msgid "Enable / Disable centered scaling for objects."
msgstr ""

#: includes/wcdp-admin-menus.php:846
msgid "Add centered objects:"
msgstr ""

#: includes/wcdp-admin-menus.php:847
msgid "Enable / Disable to add the objects centered on canvas."
msgstr ""

#: includes/wcdp-admin-menus.php:852
msgid "Corners outside box:"
msgstr ""

#: includes/wcdp-admin-menus.php:853
msgid "Enable / Disable corners outside the object's controlling box."
msgstr ""

#: includes/wcdp-admin-menus.php:858
msgid "Hide middle corners:"
msgstr ""

#: includes/wcdp-admin-menus.php:859
msgid "Hide the middle scaling corners of object's controlling box."
msgstr ""

#: includes/wcdp-admin-menus.php:864
msgid "Corners size:"
msgstr ""

#: includes/wcdp-admin-menus.php:865
msgid "Size of object's controlling corners. Default 20."
msgstr ""

#: includes/wcdp-admin-menus.php:870
msgid "Corners style:"
msgstr ""

#: includes/wcdp-admin-menus.php:871
msgid "Style of object's controlling corners."
msgstr ""

#: includes/wcdp-admin-menus.php:873
msgid "Rect"
msgstr ""

#: includes/wcdp-admin-menus.php:874
msgid "Circle"
msgstr ""

#: includes/wcdp-admin-menus.php:880
msgid "Corners border:"
msgstr ""

#: includes/wcdp-admin-menus.php:881
msgid "Enable / Disable to add borders the object's control corners."
msgstr ""

#: includes/wcdp-admin-menus.php:886
msgid "Corners color:"
msgstr ""

#: includes/wcdp-admin-menus.php:888
msgid "Corners"
msgstr ""

#: includes/wcdp-admin-menus.php:889
msgid "Corners border"
msgstr ""

#: includes/wcdp-admin-menus.php:890
msgid "Icons color"
msgstr ""

#: includes/wcdp-admin-menus.php:896
msgid "Show pop-up thumbnails:"
msgstr ""

#: includes/wcdp-admin-menus.php:897
msgid "Enable / Disable pop-up for preview the thumbnails."
msgstr ""

#: includes/wcdp-admin-menus.php:902
msgid "Hide note boxes:"
msgstr ""

#: includes/wcdp-admin-menus.php:903
msgid "Hide the note boxes of the QR Code & Map tabs."
msgstr ""

#: includes/wcdp-admin-menus.php:908
msgid "Show picker palette:"
msgstr ""

#: includes/wcdp-admin-menus.php:909
msgid "Enable / Disable colors picker palette."
msgstr ""

#: includes/wcdp-admin-menus.php:914
msgid "Columns for colors picker palette:"
msgstr ""

#: includes/wcdp-admin-menus.php:915
msgid ""
"Number of columns that will display the colors of the picker palette. "
"Default 3 colums."
msgstr ""

#: includes/wcdp-admin-menus.php:920
msgid "Add color picker"
msgstr ""

#: includes/wcdp-admin-menus.php:921
msgid "Customize the color picker:"
msgstr ""

#: includes/wcdp-admin-menus.php:926
msgid "Add color background"
msgstr ""

#: includes/wcdp-admin-menus.php:927
msgid "Customize the background color section:"
msgstr ""

#: includes/wcdp-admin-menus.php:960
msgid "Envato Username:"
msgstr ""

#: includes/wcdp-admin-menus.php:961
msgid "Add envato username."
msgstr ""

#: includes/wcdp-admin-menus.php:965
msgid "Token Key:"
msgstr ""

#: includes/wcdp-admin-menus.php:966
msgid "How to get an Token Key? follow the instructions in the"
msgstr ""

#: includes/wcdp-admin-menus.php:966
msgid "User Manual"
msgstr ""

#: includes/wcdp-admin-menus.php:966
msgid "on the license tab."
msgstr ""

#: includes/wcdp-admin-menus.php:970
msgid "Purchase Code:"
msgstr ""

#: includes/wcdp-admin-menus.php:971
msgid "Where is my Purchase Code? follow the instructions of"
msgstr ""

#: includes/wcdp-admin-menus.php:1023
msgid "ImageMagick is installed."
msgstr ""

#: includes/wcdp-admin-menus.php:1026
msgid "ImageMagick is not installed."
msgstr ""

#: includes/wcdp-admin-menus.php:1031 includes/wcdp-admin-menus.php:1059
#: includes/wcdp-manage-filters.php:25 includes/wcdp-manage-shapes.php:21
#: includes/wcdp-metabox-designs.php:440 includes/wcdp-metabox-params.php:278
#: includes/wcdp-metabox-params.php:314 includes/wcdp-metabox-params.php:320
#: includes/wcdp-metabox-params.php:326 includes/wcdp-metabox-params.php:332
#: includes/wcdp-metabox-params.php:380 includes/wcdp-metabox-params.php:386
#: includes/wcdp-metabox-params.php:422 includes/wcdp-metabox-params.php:428
#: includes/wcdp-metabox-params.php:434 includes/wcdp-metabox-params.php:440
#: includes/wcdp-metabox-params.php:446 includes/wcdp-metabox-params.php:452
#: includes/wcdp-metabox-params.php:458 includes/wcdp-metabox-params.php:464
#: includes/wcdp-metabox-params.php:470 includes/wcdp-metabox-params.php:482
#: includes/wcdp-metabox-params.php:488 includes/wcdp-metabox-params.php:494
#: includes/wcdp-metabox-params.php:506 includes/wcdp-metabox-params.php:512
#: includes/wcdp-metabox-params.php:518
msgid "Enable"
msgstr ""

#: includes/wcdp-admin-menus.php:1047 includes/wcdp-metabox-params.php:590
#, php-format
msgid "Upload %s"
msgstr ""

#: includes/wcdp-admin-menus.php:1048 includes/wcdp-metabox-params.php:591
#, php-format
msgid "Remove %s"
msgstr ""

#: includes/wcdp-admin-menus.php:1088 includes/wcdp-metabox-calendars.php:55
#: includes/wcdp-metabox-cliparts.php:55 includes/wcdp-translations.php:60
#: includes/wcdp-upload-fonts.php:45
msgid "Remove"
msgstr ""

#: includes/wcdp-content-editor.php:22 includes/wcdp-translations.php:141
msgid "Keyboard shortcuts"
msgstr ""

#: includes/wcdp-content-editor.php:23 includes/wcdp-translations.php:113
msgid "Select all"
msgstr ""

#: includes/wcdp-content-editor.php:24 includes/wcdp-translations.php:114
msgid "Erase all"
msgstr ""

#: includes/wcdp-content-editor.php:25 includes/wcdp-translations.php:115
msgid "Grid"
msgstr ""

#: includes/wcdp-content-editor.php:26 includes/wcdp-translations.php:117
msgid "Center horizontally"
msgstr ""

#: includes/wcdp-content-editor.php:27 includes/wcdp-translations.php:116
msgid "Center vertically"
msgstr ""

#: includes/wcdp-content-editor.php:28 includes/wcdp-translations.php:119
msgid "Flip horizontally"
msgstr ""

#: includes/wcdp-content-editor.php:29 includes/wcdp-translations.php:118
msgid "Flip vertically"
msgstr ""

#: includes/wcdp-content-editor.php:30 includes/wcdp-translations.php:129
msgid "Rotate"
msgstr ""

#: includes/wcdp-content-editor.php:31 includes/wcdp-translations.php:120
msgid "Bring to front"
msgstr ""

#: includes/wcdp-content-editor.php:32 includes/wcdp-translations.php:121
msgid "Send to back"
msgstr ""

#: includes/wcdp-content-editor.php:33 includes/wcdp-translations.php:86
msgid "Lock"
msgstr ""

#: includes/wcdp-content-editor.php:34 includes/wcdp-translations.php:88
msgid "Group"
msgstr ""

#: includes/wcdp-content-editor.php:35 includes/wcdp-duplicate-design.php:89
#: includes/wcdp-translations.php:123
msgid "Duplicate"
msgstr ""

#: includes/wcdp-content-editor.php:36 includes/wcdp-translations.php:134
#: includes/wcdp-translations.php:172
msgid "Delete"
msgstr ""

#: includes/wcdp-content-editor.php:37 includes/wcdp-translations.php:135
msgid "Undo"
msgstr ""

#: includes/wcdp-content-editor.php:38 includes/wcdp-translations.php:136
msgid "Redo"
msgstr ""

#: includes/wcdp-content-editor.php:39 includes/wcdp-translations.php:90
msgid "Preview"
msgstr ""

#: includes/wcdp-content-editor.php:52
msgid "Images"
msgstr ""

#: includes/wcdp-content-editor.php:55 includes/wcdp-translations.php:32
msgid "QR Code"
msgstr ""

#: includes/wcdp-content-editor.php:56 includes/wcdp-translations.php:33
msgid "Map"
msgstr ""

#: includes/wcdp-content-editor.php:58
msgid "Background colors"
msgstr ""

#: includes/wcdp-content-editor.php:59
msgid "Manage layers"
msgstr ""

#: includes/wcdp-content-editor.php:63
msgid "SAVE"
msgstr ""

#: includes/wcdp-content-editor.php:71
msgid "Templates"
msgstr ""

#: includes/wcdp-content-editor.php:74
msgid "ADD TO CART"
msgstr ""

#: includes/wcdp-content-editor.php:75
msgid "Product"
msgstr ""

#: includes/wcdp-content-editor.php:77 includes/wcdp-functions.php:383
msgid "My designs"
msgstr ""

#: includes/wcdp-content-editor.php:80
msgid "Execute code"
msgstr ""

#: includes/wcdp-content-editor.php:84
msgid "Bold"
msgstr ""

#: includes/wcdp-content-editor.php:85
msgid "Italic"
msgstr ""

#: includes/wcdp-content-editor.php:86
msgid "Underline"
msgstr ""

#: includes/wcdp-content-editor.php:87
msgid "Line through"
msgstr ""

#: includes/wcdp-content-editor.php:88
msgid "Overline"
msgstr ""

#: includes/wcdp-content-editor.php:89
msgid "Align left"
msgstr ""

#: includes/wcdp-content-editor.php:90
msgid "Align center"
msgstr ""

#: includes/wcdp-content-editor.php:91
msgid "Align right"
msgstr ""

#: includes/wcdp-content-editor.php:103
msgid "Text color"
msgstr ""

#: includes/wcdp-content-editor.php:105 includes/wcdp-content-editor.php:171
msgid "Outline"
msgstr ""

#: includes/wcdp-content-editor.php:107 includes/wcdp-content-editor.php:173
msgid "Outline color"
msgstr ""

#: includes/wcdp-content-editor.php:109
msgid "Text effects"
msgstr ""

#: includes/wcdp-content-editor.php:111
msgid "Curved"
msgstr ""

#: includes/wcdp-content-editor.php:112
msgid "Reverse"
msgstr ""

#: includes/wcdp-content-editor.php:113
msgid "Arc"
msgstr ""

#: includes/wcdp-content-editor.php:114
msgid "Small to large"
msgstr ""

#: includes/wcdp-content-editor.php:115
msgid "Large to small"
msgstr ""

#: includes/wcdp-content-editor.php:116
msgid "Bulge"
msgstr ""

#: includes/wcdp-content-editor.php:119
msgid "Radius"
msgstr ""

#: includes/wcdp-content-editor.php:121
msgid "Spacing"
msgstr ""

#: includes/wcdp-content-editor.php:124 includes/wcdp-content-editor.php:162
#: includes/wcdp-content-editor.php:175 includes/wcdp-content-editor.php:190
msgid "Opacity"
msgstr ""

#: includes/wcdp-content-editor.php:130 includes/wcdp-content-editor.php:153
#: includes/wcdp-content-editor.php:181
msgid "Fill options"
msgstr ""

#: includes/wcdp-content-editor.php:131 includes/wcdp-translations.php:81
msgid "Crop"
msgstr ""

#: includes/wcdp-content-editor.php:136
msgid "Mask"
msgstr ""

#: includes/wcdp-content-editor.php:141 includes/wcdp-content-editor.php:611
msgid "Filters"
msgstr ""

#: includes/wcdp-content-editor.php:144
msgid "Set overlay"
msgstr ""

#: includes/wcdp-content-editor.php:148
msgid "Set background"
msgstr ""

#: includes/wcdp-content-editor.php:149
msgid "Background angle"
msgstr ""

#: includes/wcdp-content-editor.php:154 includes/wcdp-content-editor.php:182
msgid "Fill color SVG"
msgstr ""

#: includes/wcdp-content-editor.php:157 includes/wcdp-content-editor.php:185
msgid "Outline SVG"
msgstr ""

#: includes/wcdp-content-editor.php:159 includes/wcdp-content-editor.php:187
msgid "Outline color SVG"
msgstr ""

#: includes/wcdp-content-editor.php:169
msgid "Fill color"
msgstr ""

#: includes/wcdp-content-editor.php:197
msgid "Foreground color"
msgstr ""

#: includes/wcdp-content-editor.php:199 includes/wcdp-content-editor.php:238
msgid "Background color"
msgstr ""

#: includes/wcdp-content-editor.php:201
msgid "Correction level"
msgstr ""

#: includes/wcdp-content-editor.php:202
msgid "Low"
msgstr ""

#: includes/wcdp-content-editor.php:203
msgid "Medium"
msgstr ""

#: includes/wcdp-content-editor.php:204
msgid "Quartile"
msgstr ""

#: includes/wcdp-content-editor.php:205
msgid "High"
msgstr ""

#: includes/wcdp-content-editor.php:209
msgid "Version range"
msgstr ""

#: includes/wcdp-content-editor.php:216
msgid "Map type"
msgstr ""

#: includes/wcdp-content-editor.php:217
msgid "Roadmap"
msgstr ""

#: includes/wcdp-content-editor.php:218
msgid "Satellite"
msgstr ""

#: includes/wcdp-content-editor.php:219
msgid "Terrain"
msgstr ""

#: includes/wcdp-content-editor.php:220
msgid "Hybrid"
msgstr ""

#: includes/wcdp-content-editor.php:222
msgid "Map zoom"
msgstr ""

#: includes/wcdp-content-editor.php:224
msgid "Icon Label"
msgstr ""

#: includes/wcdp-content-editor.php:226
msgid "Icon Size"
msgstr ""

#: includes/wcdp-content-editor.php:227
msgid "Normal"
msgstr ""

#: includes/wcdp-content-editor.php:228
msgid "Mid"
msgstr ""

#: includes/wcdp-content-editor.php:229
msgid "Small"
msgstr ""

#: includes/wcdp-content-editor.php:231
msgid "Icon color"
msgstr ""

#: includes/wcdp-content-editor.php:345
msgid "No colors found, check attribute actions in the product"
msgstr ""

#: includes/wcdp-content-editor.php:379 includes/wcdp-translations.php:100
msgid "This product is currently out of stock and unavailable."
msgstr ""

#: includes/wcdp-content-editor.php:390
msgid "Quantity"
msgstr ""

#: includes/wcdp-content-editor.php:401 includes/wcdp-content-editor.php:667
#: includes/wcdp-content-editor.php:782
msgid "Search..."
msgstr ""

#: includes/wcdp-content-editor.php:402
msgid "Search templates"
msgstr ""

#: includes/wcdp-content-editor.php:446 includes/wcdp-content-editor.php:701
#: includes/wcdp-content-editor.php:814
msgid "The category is empty"
msgstr ""

#: includes/wcdp-content-editor.php:480
msgid "Select Font"
msgstr ""

#: includes/wcdp-content-editor.php:514
msgid "ADD NEW TEXT"
msgstr ""

#: includes/wcdp-content-editor.php:525
msgid "Upload"
msgstr ""

#: includes/wcdp-content-editor.php:526
msgid "Pixabay"
msgstr ""

#: includes/wcdp-content-editor.php:527
msgid "Unsplash"
msgstr ""

#: includes/wcdp-content-editor.php:528
msgid "Pexels"
msgstr ""

#: includes/wcdp-content-editor.php:529
msgid "Flaticon"
msgstr ""

#: includes/wcdp-content-editor.php:545
msgid "Search images"
msgstr ""

#: includes/wcdp-content-editor.php:561
msgid "No uploaded images found"
msgstr ""

#: includes/wcdp-content-editor.php:567
msgid "UPLOAD IMAGE"
msgstr ""

#: includes/wcdp-content-editor.php:599
msgid "Mask layer"
msgstr ""

#: includes/wcdp-content-editor.php:603
msgid "REMOVE MASK"
msgstr ""

#: includes/wcdp-content-editor.php:655
msgid "No shapes found"
msgstr ""

#: includes/wcdp-content-editor.php:668
msgid "Search cliparts"
msgstr ""

#: includes/wcdp-content-editor.php:709 includes/wcdp-metabox-cliparts.php:62
#: includes/wcdp-translations.php:11
msgid "No uploaded cliparts found"
msgstr ""

#: includes/wcdp-content-editor.php:737
msgid "http://www.your-web-url"
msgstr ""

#: includes/wcdp-content-editor.php:738
msgid "Generate QR code"
msgstr ""

#: includes/wcdp-content-editor.php:746
msgid "Note!! QR codes are not always legible. Try it before continuing."
msgstr ""

#: includes/wcdp-content-editor.php:755
msgid "Add your address"
msgstr ""

#: includes/wcdp-content-editor.php:756
msgid "Make map"
msgstr ""

#: includes/wcdp-content-editor.php:773
msgid "Note!! Make sure the map match with you address."
msgstr ""

#: includes/wcdp-content-editor.php:783
msgid "Search calendars"
msgstr ""

#: includes/wcdp-content-editor.php:822 includes/wcdp-metabox-calendars.php:62
#: includes/wcdp-translations.php:13
msgid "No uploaded calendars found"
msgstr ""

#: includes/wcdp-content-editor.php:858
msgid "Lock layer to user"
msgstr ""

#: includes/wcdp-content-editor.php:859
msgid "Hide layer in the output files"
msgstr ""

#: includes/wcdp-content-editor.php:904 includes/wcdp-translations.php:66
msgid "No saved designs found"
msgstr ""

#: includes/wcdp-content-editor.php:911
msgid "Note!! You must login to access your designs."
msgstr ""

#: includes/wcdp-content-editor.php:924
msgid "DOWNLOAD"
msgstr ""

#: includes/wcdp-content-editor.php:943
msgid "Execute"
msgstr ""

#: includes/wcdp-content-editor.php:944
msgid "Get Json"
msgstr ""

#: includes/wcdp-content-editor.php:945
msgid "Add Json"
msgstr ""

#: includes/wcdp-content-editor.php:946
msgid "Get Image"
msgstr ""

#: includes/wcdp-content-editor.php:947
msgid "Clear"
msgstr ""

#: includes/wcdp-content-editor.php:1002
msgid "Zoom"
msgstr ""

#: includes/wcdp-docs.php:9
msgid "Current PHP version"
msgstr ""

#: includes/wcdp-docs.php:10
msgid "Memory limit"
msgstr ""

#: includes/wcdp-docs.php:11
msgid "PHP post max size"
msgstr ""

#: includes/wcdp-docs.php:12
msgid "PHP time limit"
msgstr ""

#: includes/wcdp-docs.php:13
msgid "PHP max input vars"
msgstr ""

#: includes/wcdp-docs.php:14
msgid "Maximum upload size"
msgstr ""

#: includes/wcdp-docs.php:15
msgid "PHP allow_url_fopen"
msgstr ""

#: includes/wcdp-docs.php:16
msgid "PHP file_put_contents"
msgstr ""

#: includes/wcdp-docs.php:17
msgid "PHP file_get_contents"
msgstr ""

#: includes/wcdp-docs.php:18
msgid "PHP Imagick extension"
msgstr ""

#: includes/wcdp-docs.php:31
msgid "Business card"
msgstr ""

#: includes/wcdp-docs.php:32
msgid "Flyer"
msgstr ""

#: includes/wcdp-docs.php:33
msgid "Poster"
msgstr ""

#: includes/wcdp-docs.php:34
msgid "Diptych brochure"
msgstr ""

#: includes/wcdp-docs.php:35
msgid "Triptych brochure"
msgstr ""

#: includes/wcdp-docs.php:36
msgid "Men's t-shirt"
msgstr ""

#: includes/wcdp-docs.php:37
msgid "Cap"
msgstr ""

#: includes/wcdp-docs.php:38
msgid "Sweatshirt"
msgstr ""

#: includes/wcdp-docs.php:39
msgid "Mug"
msgstr ""

#: includes/wcdp-docs.php:40
msgid "Mouse Pad"
msgstr ""

#: includes/wcdp-docs.php:41
msgid "Phone case"
msgstr ""

#: includes/wcdp-docs.php:42
msgid "Badge"
msgstr ""

#: includes/wcdp-docs.php:43
msgid "Sticker"
msgstr ""

#: includes/wcdp-docs.php:57
msgid ""
"Thank you for purchasing our WooCommerce Designer Pro plugin. Customize your "
"products and create your awesome online printing!!"
msgstr ""

#: includes/wcdp-docs.php:61
msgid "Demos"
msgstr ""

#: includes/wcdp-docs.php:62 includes/wcdp-docs.php:82
msgid "Tutorials"
msgstr ""

#: includes/wcdp-docs.php:63 includes/wcdp-docs.php:100
msgid "Status"
msgstr ""

#: includes/wcdp-docs.php:64 includes/wcdp-docs.php:112
msgid "Changelog"
msgstr ""

#: includes/wcdp-docs.php:69
msgid "Install demos"
msgstr ""

#: includes/wcdp-docs.php:74
msgid "Install Demo"
msgstr ""

#: includes/wcdp-docs.php:79
msgid "Install all demos"
msgstr ""

#: includes/wcdp-docs.php:104
msgid "Enabled"
msgstr ""

#: includes/wcdp-docs.php:104
msgid "Disabled"
msgstr ""

#: includes/wcdp-docs.php:115
msgid "Can't open file"
msgstr ""

#: includes/wcdp-duplicate-design.php:8
msgid "No design to duplicate has been supplied!"
msgstr ""

#: includes/wcdp-duplicate-design.php:23
msgid "(Copy)"
msgstr ""

#: includes/wcdp-duplicate-design.php:80
msgid "Design creation failed, could not find original design: "
msgstr ""

#: includes/wcdp-duplicate-design.php:89
msgid "Duplicate this design"
msgstr ""

#: includes/wcdp-functions.php:285
msgid "Error add product variations."
msgstr ""

#: includes/wcdp-functions.php:290
msgid "Installation successful."
msgstr ""

#: includes/wcdp-functions.php:293
msgid "Error add design."
msgstr ""

#: includes/wcdp-functions.php:296
msgid "Error add parameter."
msgstr ""

#: includes/wcdp-functions.php:298
msgid "Failed to complete the demo install"
msgstr ""

#: includes/wcdp-functions.php:300
msgid ""
"The installer cannot continue because WooCommerce is not installed or "
"activated."
msgstr ""

#: includes/wcdp-functions.php:405
msgid "Design temporarily unavailable."
msgstr ""

#: includes/wcdp-functions.php:522
msgid "Personalize"
msgstr ""

#: includes/wcdp-functions.php:598
msgid ""
"Select a defaults pages for the editor, it is required for this plugin to "
"work properly!"
msgstr ""

#: includes/wcdp-functions.php:598
msgid "Dismiss"
msgstr ""

#: includes/wcdp-manage-filters.php:13
msgid "Manage image filters"
msgstr ""

#: includes/wcdp-manage-filters.php:34 includes/wcdp-manage-shapes.php:26
#: includes/wcdp-upload-fonts.php:65
msgid "Update"
msgstr ""

#: includes/wcdp-manage-filters.php:42
msgid "Grayscale"
msgstr ""

#: includes/wcdp-manage-filters.php:43
msgid "Sepia"
msgstr ""

#: includes/wcdp-manage-filters.php:44
msgid "Warm"
msgstr ""

#: includes/wcdp-manage-filters.php:45
msgid "Cold"
msgstr ""

#: includes/wcdp-manage-filters.php:46
msgid "Yellow"
msgstr ""

#: includes/wcdp-manage-filters.php:47
msgid "Kodachrome"
msgstr ""

#: includes/wcdp-manage-filters.php:48
msgid "Vintage"
msgstr ""

#: includes/wcdp-manage-filters.php:49
msgid "Brownie"
msgstr ""

#: includes/wcdp-manage-filters.php:50
msgid "Polaroid"
msgstr ""

#: includes/wcdp-manage-filters.php:51
msgid "Technicolor"
msgstr ""

#: includes/wcdp-manage-filters.php:52
msgid "Acid"
msgstr ""

#: includes/wcdp-manage-filters.php:53
msgid "Sea"
msgstr ""

#: includes/wcdp-manage-filters.php:54
msgid "Fantasy"
msgstr ""

#: includes/wcdp-manage-filters.php:55
msgid "Purple"
msgstr ""

#: includes/wcdp-manage-filters.php:56
msgid "Ghost"
msgstr ""

#: includes/wcdp-manage-filters.php:57
msgid "Predator"
msgstr ""

#: includes/wcdp-manage-filters.php:58
msgid "Night"
msgstr ""

#: includes/wcdp-manage-filters.php:59
msgid "Invert"
msgstr ""

#: includes/wcdp-manage-filters.php:60
msgid "Noise"
msgstr ""

#: includes/wcdp-manage-filters.php:61
msgid "Pixelate"
msgstr ""

#: includes/wcdp-manage-filters.php:62
msgid "Sharpen"
msgstr ""

#: includes/wcdp-manage-filters.php:63
msgid "Blur"
msgstr ""

#: includes/wcdp-manage-filters.php:64
msgid "Emboss"
msgstr ""

#: includes/wcdp-manage-filters.php:65
msgid "Brightness"
msgstr ""

#: includes/wcdp-manage-filters.php:66
msgid "Saturation"
msgstr ""

#: includes/wcdp-manage-filters.php:67
msgid "Contrast"
msgstr ""

#: includes/wcdp-manage-shapes.php:13
msgid "Manage shapes"
msgstr ""

#: includes/wcdp-metabox-calendars.php:7
msgid "Calendar"
msgstr ""

#: includes/wcdp-metabox-calendars.php:9 includes/wcdp-metabox-calendars.php:10
msgid "Add new calendar category"
msgstr ""

#: includes/wcdp-metabox-calendars.php:11
msgid "New Calendar"
msgstr ""

#: includes/wcdp-metabox-calendars.php:12
msgid "Edit calendar category"
msgstr ""

#: includes/wcdp-metabox-calendars.php:13
msgid "View calendar category"
msgstr ""

#: includes/wcdp-metabox-calendars.php:14
msgid "Search calendar categories"
msgstr ""

#: includes/wcdp-metabox-calendars.php:15
msgid "No calendar categories found."
msgstr ""

#: includes/wcdp-metabox-calendars.php:16
msgid "No calendar categories found in Trash."
msgstr ""

#: includes/wcdp-metabox-calendars.php:20
msgid "Settings categories calendars."
msgstr ""

#: includes/wcdp-metabox-calendars.php:70
msgid "Add new calendar"
msgstr ""

#: includes/wcdp-metabox-categories.php:6
#: includes/wcdp-metabox-categories.php:8
msgid "Design categories"
msgstr ""

#: includes/wcdp-metabox-categories.php:7
msgid "Category"
msgstr ""

#: includes/wcdp-metabox-categories.php:9
msgid "Add new category"
msgstr ""

#: includes/wcdp-metabox-categories.php:10
msgid "Edit category"
msgstr ""

#: includes/wcdp-metabox-categories.php:11
msgid "Search categories"
msgstr ""

#: includes/wcdp-metabox-categories.php:12
msgid "All categories"
msgstr ""

#: includes/wcdp-metabox-categories.php:13
msgid "Parent category"
msgstr ""

#: includes/wcdp-metabox-categories.php:14
msgid "Parent category:"
msgstr ""

#: includes/wcdp-metabox-categories.php:15
msgid "Update category"
msgstr ""

#: includes/wcdp-metabox-categories.php:16
msgid "New category name"
msgstr ""

#: includes/wcdp-metabox-cliparts.php:7
msgid "Clipart"
msgstr ""

#: includes/wcdp-metabox-cliparts.php:9 includes/wcdp-metabox-cliparts.php:10
msgid "Add new clipart category"
msgstr ""

#: includes/wcdp-metabox-cliparts.php:11
msgid "New Clipart"
msgstr ""

#: includes/wcdp-metabox-cliparts.php:12
msgid "Edit clipart category"
msgstr ""

#: includes/wcdp-metabox-cliparts.php:13
msgid "View clipart category"
msgstr ""

#: includes/wcdp-metabox-cliparts.php:14
msgid "Search clipart categories"
msgstr ""

#: includes/wcdp-metabox-cliparts.php:15
msgid "No clipart categories found."
msgstr ""

#: includes/wcdp-metabox-cliparts.php:16
msgid "No clipart categories found in Trash."
msgstr ""

#: includes/wcdp-metabox-cliparts.php:20
msgid "Settings categories cliparts."
msgstr ""

#: includes/wcdp-metabox-cliparts.php:70
msgid "Add new clipart"
msgstr ""

#: includes/wcdp-metabox-designs.php:7 includes/wcdp-metabox-designs.php:210
msgid "Design"
msgstr ""

#: includes/wcdp-metabox-designs.php:9 includes/wcdp-metabox-designs.php:10
msgid "Add new design"
msgstr ""

#: includes/wcdp-metabox-designs.php:11
msgid "New Design"
msgstr ""

#: includes/wcdp-metabox-designs.php:12
msgid "Edit design"
msgstr ""

#: includes/wcdp-metabox-designs.php:13
msgid "View design"
msgstr ""

#: includes/wcdp-metabox-designs.php:14
msgid "Search designs"
msgstr ""

#: includes/wcdp-metabox-designs.php:15
msgid "No designs found."
msgstr ""

#: includes/wcdp-metabox-designs.php:16
msgid "No designs found in Trash."
msgstr ""

#: includes/wcdp-metabox-designs.php:20
msgid "Settings designs."
msgstr ""

#: includes/wcdp-metabox-designs.php:41 includes/wcdp-translations.php:29
msgid "Image"
msgstr ""

#: includes/wcdp-metabox-designs.php:42 includes/wcdp-my-designs.php:16
#: includes/wcdp-order-design.php:128
msgid "Design Name"
msgstr ""

#: includes/wcdp-metabox-designs.php:43 includes/wcdp-metabox-params.php:7
msgid "Parameter"
msgstr ""

#: includes/wcdp-metabox-designs.php:45 includes/wcdp-my-designs.php:14
msgid "Date"
msgstr ""

#: includes/wcdp-metabox-designs.php:101
msgid "Default parameter"
msgstr ""

#: includes/wcdp-metabox-designs.php:152
msgid "Enable Custom Product."
msgstr ""

#: includes/wcdp-metabox-designs.php:156
msgid "Select main design:"
msgstr ""

#: includes/wcdp-metabox-designs.php:165
msgid "Add multiple designs to the templates section:"
msgstr ""

#: includes/wcdp-metabox-designs.php:178
msgid "Add designs to the templates section by categories:"
msgstr ""

#: includes/wcdp-metabox-designs.php:186
msgid "Load only template objects."
msgstr ""

#: includes/wcdp-metabox-designs.php:188
msgid ""
"This option will add only the front template objects to the active side of "
"the editor without loading the parameter, improving speed. Recommended to "
"add simple templates as ideas."
msgstr ""

#: includes/wcdp-metabox-designs.php:193
msgid "Load parameters by AJAX query."
msgstr ""

#: includes/wcdp-metabox-designs.php:195
msgid ""
"This option will load only the canvas sizes. If you want to load the "
"complete parameter of the designs added in multiples, leave it disabled."
msgstr ""

#: includes/wcdp-metabox-designs.php:209
msgid "Select parameter"
msgstr ""

#: includes/wcdp-metabox-designs.php:211
msgid "WCDP - Custom Product"
msgstr ""

#: includes/wcdp-metabox-designs.php:217
msgid "WCDP - Actions"
msgstr ""

#: includes/wcdp-metabox-designs.php:236
msgid "WCDP - Set attribute actions"
msgstr ""

#: includes/wcdp-metabox-designs.php:237 includes/wcdp-metabox-designs.php:249
msgid "Close"
msgstr ""

#: includes/wcdp-metabox-designs.php:239 includes/wcdp-metabox-designs.php:251
msgid "Expand"
msgstr ""

#: includes/wcdp-metabox-designs.php:245
msgid "Save actions"
msgstr ""

#: includes/wcdp-metabox-designs.php:246
msgid "Empty actions"
msgstr ""

#: includes/wcdp-metabox-designs.php:247
msgid "Refresh attributes"
msgstr ""

#: includes/wcdp-metabox-designs.php:268
msgid "Product color"
msgstr ""

#: includes/wcdp-metabox-designs.php:274
msgid "Product image: Front"
msgstr ""

#: includes/wcdp-metabox-designs.php:280
msgid "Product image: Back"
msgstr ""

#: includes/wcdp-metabox-designs.php:286
msgid "Show product sides"
msgstr ""

#: includes/wcdp-metabox-designs.php:292 includes/wcdp-metabox-params.php:242
msgid "Canvas width"
msgstr ""

#: includes/wcdp-metabox-designs.php:298 includes/wcdp-metabox-params.php:248
msgid "Canvas height"
msgstr ""

#: includes/wcdp-metabox-designs.php:304 includes/wcdp-metabox-params.php:254
msgid "Canvas output width"
msgstr ""

#: includes/wcdp-metabox-designs.php:310 includes/wcdp-metabox-params.php:291
msgid "PDF output width"
msgstr ""

#: includes/wcdp-metabox-designs.php:316 includes/wcdp-metabox-params.php:297
msgid "PDF output height"
msgstr ""

#: includes/wcdp-metabox-designs.php:322 includes/wcdp-metabox-params.php:345
msgid "Margin bleed area horizontally"
msgstr ""

#: includes/wcdp-metabox-designs.php:328 includes/wcdp-metabox-params.php:351
msgid "Margin bleed area vertically"
msgstr ""

#: includes/wcdp-metabox-designs.php:334 includes/wcdp-metabox-params.php:357
msgid "Top bleed area"
msgstr ""

#: includes/wcdp-metabox-designs.php:340 includes/wcdp-metabox-params.php:363
msgid "Left bleed area"
msgstr ""

#: includes/wcdp-metabox-designs.php:346 includes/wcdp-metabox-params.php:369
msgid "Radius bleed area"
msgstr ""

#: includes/wcdp-metabox-designs.php:352 includes/wcdp-metabox-params.php:375
msgid "Editor corners rounded"
msgstr ""

#: includes/wcdp-metabox-designs.php:358
msgid "Drop down"
msgstr ""

#: includes/wcdp-metabox-designs.php:359
msgid "Product colors"
msgstr ""

#: includes/wcdp-metabox-designs.php:360
msgid "Radio checkbox"
msgstr ""

#: includes/wcdp-metabox-designs.php:363
msgid "Background image"
msgstr ""

#: includes/wcdp-metabox-designs.php:364
msgid "Overlay image"
msgstr ""

#: includes/wcdp-metabox-designs.php:367
msgid "Select option"
msgstr ""

#: includes/wcdp-metabox-designs.php:368 includes/wcdp-metabox-params.php:221
msgid "Side front and back"
msgstr ""

#: includes/wcdp-metabox-designs.php:369 includes/wcdp-metabox-params.php:222
msgid "Only side front"
msgstr ""

#: includes/wcdp-metabox-designs.php:394
msgid "Expand / Close"
msgstr ""

#: includes/wcdp-metabox-designs.php:400
msgid "Layout type"
msgstr ""

#: includes/wcdp-metabox-designs.php:410
msgid "Set product image as"
msgstr ""

#: includes/wcdp-metabox-designs.php:420
msgid "Attribute value"
msgstr ""

#: includes/wcdp-metabox-designs.php:441
msgid "Action"
msgstr ""

#: includes/wcdp-metabox-designs.php:442
msgid "Value"
msgstr ""

#: includes/wcdp-metabox-designs.php:443
msgid "Format"
msgstr ""

#: includes/wcdp-metabox-designs.php:461
msgid "Remove image"
msgstr ""

#: includes/wcdp-metabox-designs.php:475
msgid "Empty transparent"
msgstr ""

#: includes/wcdp-metabox-designs.php:479
msgid "Color picker"
msgstr ""

#: includes/wcdp-metabox-designs.php:482
msgid "Set image"
msgstr ""

#: includes/wcdp-metabox-designs.php:485
msgid "px"
msgstr ""

#: includes/wcdp-metabox-designs.php:488
msgid "mm"
msgstr ""

#: includes/wcdp-metabox-designs.php:504
msgid "No attributes found"
msgstr ""

#: includes/wcdp-metabox-params.php:9 includes/wcdp-metabox-params.php:10
msgid "Add New parameter"
msgstr ""

#: includes/wcdp-metabox-params.php:11
msgid "New Parameter"
msgstr ""

#: includes/wcdp-metabox-params.php:12
msgid "Edit Parameter"
msgstr ""

#: includes/wcdp-metabox-params.php:13
msgid "View Parameter"
msgstr ""

#: includes/wcdp-metabox-params.php:14
msgid "Search Parameters"
msgstr ""

#: includes/wcdp-metabox-params.php:15
msgid "No Parameters found."
msgstr ""

#: includes/wcdp-metabox-params.php:16
msgid "No Parameters found in Trash."
msgstr ""

#: includes/wcdp-metabox-params.php:20
msgid "Settings product parameters."
msgstr ""

#: includes/wcdp-metabox-params.php:154
msgid "Front"
msgstr ""

#: includes/wcdp-metabox-params.php:155
msgid "Back"
msgstr ""

#: includes/wcdp-metabox-params.php:213
msgid "Select page for editor"
msgstr ""

#: includes/wcdp-metabox-params.php:218
msgid "Select type of design"
msgstr ""

#: includes/wcdp-metabox-params.php:219
msgid "Select type of design for editor."
msgstr ""

#: includes/wcdp-metabox-params.php:223
msgid "Diptych Brochure"
msgstr ""

#: includes/wcdp-metabox-params.php:224
msgid "Triptych Brochure"
msgstr ""

#: includes/wcdp-metabox-params.php:230
msgid "Name front side"
msgstr ""

#: includes/wcdp-metabox-params.php:231
msgid "Default name \"Front\""
msgstr ""

#: includes/wcdp-metabox-params.php:236
msgid "Name back side"
msgstr ""

#: includes/wcdp-metabox-params.php:237
msgid "Default name \"Back\""
msgstr ""

#: includes/wcdp-metabox-params.php:243
msgid "Set canvas width for editor."
msgstr ""

#: includes/wcdp-metabox-params.php:249
msgid "Set canvas height for editor."
msgstr ""

#: includes/wcdp-metabox-params.php:255
msgid "Set output files width."
msgstr ""

#: includes/wcdp-metabox-params.php:260
msgid "Canvas output bleed area"
msgstr ""

#: includes/wcdp-metabox-params.php:261
msgid ""
"Capture only the inside of the bleed area in the output files. Requires "
"option Border bleed area enabled."
msgstr ""

#: includes/wcdp-metabox-params.php:267
msgid "Add watermark"
msgstr ""

#: includes/wcdp-metabox-params.php:268
msgid "Add watermark when downloading designs."
msgstr ""

#: includes/wcdp-metabox-params.php:273
msgid "Repeat watermark image"
msgstr ""

#: includes/wcdp-metabox-params.php:274
msgid "Repeat the watermark in the background."
msgstr ""

#: includes/wcdp-metabox-params.php:279
msgid "Preview design"
msgstr ""

#: includes/wcdp-metabox-params.php:280
msgid "Enable/Disable design preview button in the toolbar options."
msgstr ""

#: includes/wcdp-metabox-params.php:285
msgid "Preview design width"
msgstr ""

#: includes/wcdp-metabox-params.php:286
msgid "Set width of preview images."
msgstr ""

#: includes/wcdp-metabox-params.php:292
msgid "Set PDF output files width."
msgstr ""

#: includes/wcdp-metabox-params.php:298
msgid "Set PDF output files height."
msgstr ""

#: includes/wcdp-metabox-params.php:303
msgid "PDF margin top"
msgstr ""

#: includes/wcdp-metabox-params.php:304
msgid "Set PDF margin top. To remove margin, set value -1"
msgstr ""

#: includes/wcdp-metabox-params.php:309
msgid "PDF scale output image"
msgstr ""

#: includes/wcdp-metabox-params.php:310
msgid ""
"Output image is scaled proportionally by the provided scale factor. Default "
"0.8"
msgstr ""

#: includes/wcdp-metabox-params.php:315
msgid "PDF stretch output image"
msgstr ""

#: includes/wcdp-metabox-params.php:316
msgid "Stretch the output image to the PDF size automatically."
msgstr ""

#: includes/wcdp-metabox-params.php:321
msgid "Border bleed area"
msgstr ""

#: includes/wcdp-metabox-params.php:322
msgid "Show/Hide border bleed area in editor."
msgstr ""

#: includes/wcdp-metabox-params.php:327
msgid "Auto hide bleed area"
msgstr ""

#: includes/wcdp-metabox-params.php:328
msgid "Auto hide bleed area when there is no object selected in the editor."
msgstr ""

#: includes/wcdp-metabox-params.php:333
msgid "Clipping objects to bleed area"
msgstr ""

#: includes/wcdp-metabox-params.php:334
msgid ""
"Clipping and hide the objects outside the bleed area. Requires option Border "
"bleed area enabled."
msgstr ""

#: includes/wcdp-metabox-params.php:339
msgid "Border width bleed area"
msgstr ""

#: includes/wcdp-metabox-params.php:340
msgid "Set width of border bleed area."
msgstr ""

#: includes/wcdp-metabox-params.php:346
msgid "Set the margin bleed area from outside to inside. Left/Right"
msgstr ""

#: includes/wcdp-metabox-params.php:352
msgid "Set the margin bleed area from outside to inside. Top/Bottom"
msgstr ""

#: includes/wcdp-metabox-params.php:358
msgid "Set top position of border bleed area."
msgstr ""

#: includes/wcdp-metabox-params.php:364
msgid "Set left position of border bleed area."
msgstr ""

#: includes/wcdp-metabox-params.php:370
msgid "Set the radius bleed area."
msgstr ""

#: includes/wcdp-metabox-params.php:376
msgid "Round corners in the canvas editor."
msgstr ""

#: includes/wcdp-metabox-params.php:381
msgid "Editor border solid"
msgstr ""

#: includes/wcdp-metabox-params.php:382
msgid "Border solid in the canvas editor."
msgstr ""

#: includes/wcdp-metabox-params.php:387
msgid "Editor box shadow"
msgstr ""

#: includes/wcdp-metabox-params.php:388
msgid "Box shadow in the canvas editor."
msgstr ""

#: includes/wcdp-metabox-params.php:393
msgid "Grid size"
msgstr ""

#: includes/wcdp-metabox-params.php:394
msgid "Size of grid cells."
msgstr ""

#: includes/wcdp-metabox-params.php:398
msgid "Default font size"
msgstr ""

#: includes/wcdp-metabox-params.php:399
msgid "Default font size in the text tab."
msgstr ""

#: includes/wcdp-metabox-params.php:405
msgid "Default image size"
msgstr ""

#: includes/wcdp-metabox-params.php:406
msgid "Image size when add to the canvas. Default size 150px"
msgstr ""

#: includes/wcdp-metabox-params.php:411
msgid "Default shape size"
msgstr ""

#: includes/wcdp-metabox-params.php:412
msgid "Shape size when add to the canvas. Default size 80px"
msgstr ""

#: includes/wcdp-metabox-params.php:417
msgid "Default QR size"
msgstr ""

#: includes/wcdp-metabox-params.php:418
msgid "QR Code size when add to the canvas. Default size 80px"
msgstr ""

#: includes/wcdp-metabox-params.php:423
msgid "Group and ungroup SVG"
msgstr ""

#: includes/wcdp-metabox-params.php:424
msgid "Enable/Disable for the user the option to group and ungroup SVG."
msgstr ""

#: includes/wcdp-metabox-params.php:429
msgid "Mask layers"
msgstr ""

#: includes/wcdp-metabox-params.php:430
msgid "Enable/Disable for user the option to mask layers."
msgstr ""

#: includes/wcdp-metabox-params.php:435
msgid "Add or extract background images"
msgstr ""

#: includes/wcdp-metabox-params.php:436
msgid "Enable/Disable for user the option to add or extract background images."
msgstr ""

#: includes/wcdp-metabox-params.php:441
msgid "Hide background color in the output files"
msgstr ""

#: includes/wcdp-metabox-params.php:442
msgid ""
"Hide the background color of the design when generating the output files."
msgstr ""

#: includes/wcdp-metabox-params.php:447
msgid "Hide background image in the output files"
msgstr ""

#: includes/wcdp-metabox-params.php:448
msgid ""
"Hide the background image of the design when generating the output files."
msgstr ""

#: includes/wcdp-metabox-params.php:453
msgid "Hide overlay image in the output files"
msgstr ""

#: includes/wcdp-metabox-params.php:454
msgid "Hide the overlay image of the design when generating the output files."
msgstr ""

#: includes/wcdp-metabox-params.php:459
msgid "Shapes tab"
msgstr ""

#: includes/wcdp-metabox-params.php:460
msgid "Enable/Disable the section of geometric shapes."
msgstr ""

#: includes/wcdp-metabox-params.php:465
msgid "Cliparts tab"
msgstr ""

#: includes/wcdp-metabox-params.php:466
msgid "Enable/Disable the section of cliparts."
msgstr ""

#: includes/wcdp-metabox-params.php:471
msgid "Add all cliparts categories"
msgstr ""

#: includes/wcdp-metabox-params.php:472
#, php-format
msgid "Add all %1$scategories%2$s to the cliparts section."
msgstr ""

#: includes/wcdp-metabox-params.php:472 includes/wcdp-metabox-params.php:478
msgid "Add new category of cliparts"
msgstr ""

#: includes/wcdp-metabox-params.php:477
msgid "Add cliparts categories"
msgstr ""

#: includes/wcdp-metabox-params.php:478
#, php-format
msgid "Add %1$scategories%2$s to the cliparts section."
msgstr ""

#: includes/wcdp-metabox-params.php:483
msgid "QR tab"
msgstr ""

#: includes/wcdp-metabox-params.php:484
msgid "Enable/Disable the section of qr."
msgstr ""

#: includes/wcdp-metabox-params.php:489
msgid "Calendars tab"
msgstr ""

#: includes/wcdp-metabox-params.php:490
msgid "Enable/Disable the section of calendars."
msgstr ""

#: includes/wcdp-metabox-params.php:495
msgid "Add all calendars categories"
msgstr ""

#: includes/wcdp-metabox-params.php:496
#, php-format
msgid "Add all %1$scategories%2$s to the calendars section."
msgstr ""

#: includes/wcdp-metabox-params.php:496 includes/wcdp-metabox-params.php:502
msgid "Add new category of calendars"
msgstr ""

#: includes/wcdp-metabox-params.php:501
msgid "Add calendars categories"
msgstr ""

#: includes/wcdp-metabox-params.php:502
#, php-format
msgid "Add %1$scategories%2$s to the calendars section."
msgstr ""

#: includes/wcdp-metabox-params.php:507
msgid "Background colors tab"
msgstr ""

#: includes/wcdp-metabox-params.php:508
msgid "Enable/Disable the section of background colors."
msgstr ""

#: includes/wcdp-metabox-params.php:513
msgid "Layers tab"
msgstr ""

#: includes/wcdp-metabox-params.php:514
msgid "Enable/Disable the section of manage layers."
msgstr ""

#: includes/wcdp-metabox-params.php:519
msgid "Static maps tab"
msgstr ""

#: includes/wcdp-metabox-params.php:520
msgid "Enable/Disable the section of static maps."
msgstr ""

#: includes/wcdp-metabox-params.php:525
msgid "Static maps width"
msgstr ""

#: includes/wcdp-metabox-params.php:526
msgid "Set width of static maps."
msgstr ""

#: includes/wcdp-metabox-params.php:531
msgid "Static maps height"
msgstr ""

#: includes/wcdp-metabox-params.php:532
msgid "Set height of static maps."
msgstr ""

#: includes/wcdp-metabox-params.php:614
msgid "Settings Parameters"
msgstr ""

#: includes/wcdp-my-designs.php:15 includes/wcdp-order-design.php:127
msgid "Preview Design"
msgstr ""

#: includes/wcdp-my-designs.php:17
msgid "Product ID"
msgstr ""

#: includes/wcdp-my-designs.php:18 includes/wcdp-order-design.php:129
msgid "Options"
msgstr ""

#: includes/wcdp-my-designs.php:41
msgid "LOAD"
msgstr ""

#: includes/wcdp-my-designs.php:42
msgid "RENAME"
msgstr ""

#: includes/wcdp-my-designs.php:43
msgid "DELETE"
msgstr ""

#: includes/wcdp-my-designs.php:53
msgid "No saved designs found."
msgstr ""

#: includes/wcdp-my-designs.php:56
msgid "You must"
msgstr ""

#: includes/wcdp-my-designs.php:56
msgid "login"
msgstr ""

#: includes/wcdp-my-designs.php:56
msgid "to access your designs."
msgstr ""

#: includes/wcdp-order-design.php:66 includes/wcdp-order-design.php:139
msgid "Download"
msgstr ""

#: includes/wcdp-order-design.php:80 includes/wcdp-order-design.php:95
msgid "Download custom design"
msgstr ""

#: includes/wcdp-order-design.php:124
msgid "Custom design"
msgstr ""

#: includes/wcdp-order-design.php:143
msgid "You deleted design and download is not available."
msgstr ""

#: includes/wcdp-translations.php:6
msgid "Files supported: ttf,otf,eot,woff,woff2"
msgstr ""

#: includes/wcdp-translations.php:7
msgid "Search in"
msgstr ""

#: includes/wcdp-translations.php:8 includes/wcdp-upload-fonts.php:54
msgid "No selected fonts found"
msgstr ""

#: includes/wcdp-translations.php:9
msgid "This font is already selected"
msgstr ""

#: includes/wcdp-translations.php:10
msgid "No templates found in search"
msgstr ""

#: includes/wcdp-translations.php:12
msgid "No cliparts found in search"
msgstr ""

#: includes/wcdp-translations.php:14
msgid "No calendars found in search"
msgstr ""

#: includes/wcdp-translations.php:15
msgid "No images found in search"
msgstr ""

#: includes/wcdp-translations.php:16
msgid "Please choose at least one file"
msgstr ""

#: includes/wcdp-translations.php:17
msgid "No layers"
msgstr ""

#: includes/wcdp-translations.php:18
msgid "Cover the image with the mask layer"
msgstr ""

#: includes/wcdp-translations.php:19
msgid "Select mask layer"
msgstr ""

#: includes/wcdp-translations.php:20
msgid "New layer"
msgstr ""

#: includes/wcdp-translations.php:21
msgid "Layer options"
msgstr ""

#: includes/wcdp-translations.php:22
msgid "Show layer in the output files"
msgstr ""

#: includes/wcdp-translations.php:23
msgid "Sorting layer"
msgstr ""

#: includes/wcdp-translations.php:24
msgid "Lock layer"
msgstr ""

#: includes/wcdp-translations.php:25
msgid "Unlock layer"
msgstr ""

#: includes/wcdp-translations.php:26
msgid "Unlock layer to user"
msgstr ""

#: includes/wcdp-translations.php:27
msgid "Delete layer"
msgstr ""

#: includes/wcdp-translations.php:30
msgid "Vector"
msgstr ""

#: includes/wcdp-translations.php:31
msgid "Shape"
msgstr ""

#: includes/wcdp-translations.php:34
msgid ""
"There is a conflict with the selected CMYK / RGB profiles.<br>They may be "
"damaged or reversed. Check the settings."
msgstr ""

#: includes/wcdp-translations.php:35
msgid "ImageMagick is disabled or not installed."
msgstr ""

#: includes/wcdp-translations.php:36
msgid "Select a file to upload"
msgstr ""

#: includes/wcdp-translations.php:37
msgid "Use this file"
msgstr ""

#: includes/wcdp-translations.php:38
msgid "Files supported:"
msgstr ""

#: includes/wcdp-translations.php:39
msgid "File is not supported. Please upload a valid file."
msgstr ""

#: includes/wcdp-translations.php:40
msgid "ATTENTION!! ARE YOU SURE?"
msgstr ""

#: includes/wcdp-translations.php:41
msgid "Restore successful."
msgstr ""

#: includes/wcdp-translations.php:42
msgid "You will proceed to update the color picker table with the profiles"
msgstr ""

#: includes/wcdp-translations.php:43
msgid ""
"If the profiles are not correct, make sure to save the changes after "
"selecting them. Keep in mind that generating a new table may take a few "
"minutes depending on the speed and memory of your server. If everything is "
"correct, you can continue."
msgstr ""

#: includes/wcdp-translations.php:44
msgid "Update successful."
msgstr ""

#: includes/wcdp-translations.php:45
msgid "UPDATING COLORS PICKER TABLE"
msgstr ""

#: includes/wcdp-translations.php:46
msgid "CANCELED UPDATE!!"
msgstr ""

#: includes/wcdp-translations.php:47
msgid ""
"Max colors chunk by conversion is too high.<br>Try change to a lower value."
msgstr ""

#: includes/wcdp-translations.php:48
msgid "Save design successful."
msgstr ""

#: includes/wcdp-translations.php:49 includes/wcdp-translations.php:79
msgid "Save"
msgstr ""

#: includes/wcdp-translations.php:50
msgid "Enter a title for the design (Optional):"
msgstr ""

#: includes/wcdp-translations.php:51
msgid "Enter a new title for the design:"
msgstr ""

#: includes/wcdp-translations.php:52
msgid "Untitled"
msgstr ""

#: includes/wcdp-translations.php:53
msgid "Saving the design wait a moment"
msgstr ""

#: includes/wcdp-translations.php:54
msgid "Saving files wait a moment"
msgstr ""

#: includes/wcdp-translations.php:55
msgid "Compressing files wait a moment"
msgstr ""

#: includes/wcdp-translations.php:56
msgid "Processing wait a moment"
msgstr ""

#: includes/wcdp-translations.php:57
msgid "There was an error in the process, please try again."
msgstr ""

#: includes/wcdp-translations.php:58
msgid "Error reset to defaults."
msgstr ""

#: includes/wcdp-translations.php:59
msgid "Error loading file"
msgstr ""

#: includes/wcdp-translations.php:61
msgid "Enter your text"
msgstr ""

#: includes/wcdp-translations.php:62
msgid "Are you sure to erase all objects?"
msgstr ""

#: includes/wcdp-translations.php:63
msgid "You want to load this design?"
msgstr ""

#: includes/wcdp-translations.php:64
msgid "Are you sure to remove this design?"
msgstr ""

#: includes/wcdp-translations.php:65
msgid "Delete successful"
msgstr ""

#: includes/wcdp-translations.php:67
msgid "You cannot save more designs the maximum allowed is"
msgstr ""

#: includes/wcdp-translations.php:68
msgid "Do you want to save the design?"
msgstr ""

#: includes/wcdp-translations.php:69
msgid "Do you want to download the design?"
msgstr ""

#: includes/wcdp-translations.php:70
msgid "Cancel"
msgstr ""

#: includes/wcdp-translations.php:71
msgid "Confirm!"
msgstr ""

#: includes/wcdp-translations.php:72
msgid "Install"
msgstr ""

#: includes/wcdp-translations.php:73
msgid "All demos"
msgstr ""

#: includes/wcdp-translations.php:74
msgid "Installation of the demo"
msgstr ""

#: includes/wcdp-translations.php:75
msgid "Select a page where you want to display the editor"
msgstr ""

#: includes/wcdp-translations.php:76
msgid ""
"The installer cannot continue because no page is selected for the editor."
msgstr ""

#: includes/wcdp-translations.php:77
msgid "Installing demo"
msgstr ""

#: includes/wcdp-translations.php:78
msgid "wait a moment"
msgstr ""

#: includes/wcdp-translations.php:80
msgid "Move"
msgstr ""

#: includes/wcdp-translations.php:82
msgid "Zoom In"
msgstr ""

#: includes/wcdp-translations.php:83
msgid "Zoom Out"
msgstr ""

#: includes/wcdp-translations.php:84
msgid "Square"
msgstr ""

#: includes/wcdp-translations.php:85
msgid "Reset"
msgstr ""

#: includes/wcdp-translations.php:87
msgid "Unlock"
msgstr ""

#: includes/wcdp-translations.php:89
msgid "Ungroup"
msgstr ""

#: includes/wcdp-translations.php:91
msgid "Static Maps API not found please check the settings."
msgstr ""

#: includes/wcdp-translations.php:92
msgid "Product ID:"
msgstr ""

#: includes/wcdp-translations.php:93
msgid "Template ID:"
msgstr ""

#: includes/wcdp-translations.php:94
msgid "You want to load this template?"
msgstr ""

#: includes/wcdp-translations.php:95
msgid "not available, for more information please contact the administrator."
msgstr ""

#: includes/wcdp-translations.php:96
msgid "Choose the product options before adding this product to your cart."
msgstr ""

#: includes/wcdp-translations.php:97
msgid "Product successfully added to shopping cart."
msgstr ""

#: includes/wcdp-translations.php:98
msgid "View my cart?"
msgstr ""

#: includes/wcdp-translations.php:99
msgid "The quantity must be greater than or equal to 1."
msgstr ""

#: includes/wcdp-translations.php:109
msgid "Move up"
msgstr ""

#: includes/wcdp-translations.php:110
msgid "Move down"
msgstr ""

#: includes/wcdp-translations.php:111
msgid "Move left"
msgstr ""

#: includes/wcdp-translations.php:112
msgid "Move right"
msgstr ""

#: includes/wcdp-translations.php:122
msgid "Lock/Unlock"
msgstr ""

#: includes/wcdp-translations.php:124
msgid "Duplicate from side to side"
msgstr ""

#: includes/wcdp-translations.php:125
msgid "Align vertically with more space"
msgstr ""

#: includes/wcdp-translations.php:126
msgid "Align vertically with less space"
msgstr ""

#: includes/wcdp-translations.php:127
msgid "Align to left"
msgstr ""

#: includes/wcdp-translations.php:128
msgid "Align to right"
msgstr ""

#: includes/wcdp-translations.php:130
msgid "Return to original state"
msgstr ""

#: includes/wcdp-translations.php:131
msgid "Align vertically"
msgstr ""

#: includes/wcdp-translations.php:132
msgid "Align horizontally"
msgstr ""

#: includes/wcdp-translations.php:133
msgid "Group/Ungroup"
msgstr ""

#: includes/wcdp-translations.php:137
msgid "Smaller line of text"
msgstr ""

#: includes/wcdp-translations.php:138
msgid "Larger line of text"
msgstr ""

#: includes/wcdp-translations.php:139
msgid "key is already in use, try another combination."
msgstr ""

#: includes/wcdp-translations.php:140
msgid "No keyboard shortcuts found!"
msgstr ""

#: includes/wcdp-translations.php:145
msgid "break"
msgstr ""

#: includes/wcdp-translations.php:146
msgid "backspace / delete"
msgstr ""

#: includes/wcdp-translations.php:147
msgid "tab"
msgstr ""

#: includes/wcdp-translations.php:148
msgid "clear"
msgstr ""

#: includes/wcdp-translations.php:149
msgid "enter"
msgstr ""

#: includes/wcdp-translations.php:150
msgid "shift"
msgstr ""

#: includes/wcdp-translations.php:151
msgid "ctrl"
msgstr ""

#: includes/wcdp-translations.php:152
msgid "alt"
msgstr ""

#: includes/wcdp-translations.php:153
msgid "pause/break"
msgstr ""

#: includes/wcdp-translations.php:154
msgid "caps lock"
msgstr ""

#: includes/wcdp-translations.php:155
msgid "escape"
msgstr ""

#: includes/wcdp-translations.php:156
msgid "conversion"
msgstr ""

#: includes/wcdp-translations.php:157
msgid "non-conversion"
msgstr ""

#: includes/wcdp-translations.php:158
msgid "spacebar"
msgstr ""

#: includes/wcdp-translations.php:159
msgid "page up"
msgstr ""

#: includes/wcdp-translations.php:160
msgid "page down"
msgstr ""

#: includes/wcdp-translations.php:161
msgid "end"
msgstr ""

#: includes/wcdp-translations.php:162
msgid "home"
msgstr ""

#: includes/wcdp-translations.php:163
msgid "Left arrow"
msgstr ""

#: includes/wcdp-translations.php:164
msgid "Up arrow"
msgstr ""

#: includes/wcdp-translations.php:165
msgid "Right arrow"
msgstr ""

#: includes/wcdp-translations.php:166
msgid "Down arrow"
msgstr ""

#: includes/wcdp-translations.php:167
msgid "select"
msgstr ""

#: includes/wcdp-translations.php:168
msgid "print"
msgstr ""

#: includes/wcdp-translations.php:169
msgid "execute"
msgstr ""

#: includes/wcdp-translations.php:170
msgid "Print Screen"
msgstr ""

#: includes/wcdp-translations.php:171
msgid "insert"
msgstr ""

#: includes/wcdp-translations.php:184
msgid "semicolon (firefox), equals"
msgstr ""

#: includes/wcdp-translations.php:186
msgid "equals (firefox)"
msgstr ""

#: includes/wcdp-translations.php:215
msgid "Windows Key / Left ⌘ / Chromebook Search key"
msgstr ""

#: includes/wcdp-translations.php:216
msgid "right window key"
msgstr ""

#: includes/wcdp-translations.php:217
msgid "Windows Menu / Right ⌘"
msgstr ""

#: includes/wcdp-translations.php:218
msgid "numpad 0"
msgstr ""

#: includes/wcdp-translations.php:219
msgid "numpad 1"
msgstr ""

#: includes/wcdp-translations.php:220
msgid "numpad 2"
msgstr ""

#: includes/wcdp-translations.php:221
msgid "numpad 3"
msgstr ""

#: includes/wcdp-translations.php:222
msgid "numpad 4"
msgstr ""

#: includes/wcdp-translations.php:223
msgid "numpad 5"
msgstr ""

#: includes/wcdp-translations.php:224
msgid "numpad 6"
msgstr ""

#: includes/wcdp-translations.php:225
msgid "numpad 7"
msgstr ""

#: includes/wcdp-translations.php:226
msgid "numpad 8"
msgstr ""

#: includes/wcdp-translations.php:227
msgid "numpad 9"
msgstr ""

#: includes/wcdp-translations.php:228
msgid "multiply"
msgstr ""

#: includes/wcdp-translations.php:229
msgid "add"
msgstr ""

#: includes/wcdp-translations.php:230
msgid "numpad period (firefox)"
msgstr ""

#: includes/wcdp-translations.php:231
msgid "subtract"
msgstr ""

#: includes/wcdp-translations.php:232
msgid "decimal point"
msgstr ""

#: includes/wcdp-translations.php:233
msgid "divide"
msgstr ""

#: includes/wcdp-translations.php:258
msgid "num lock"
msgstr ""

#: includes/wcdp-translations.php:259
msgid "scroll lock"
msgstr ""

#: includes/wcdp-translations.php:265
msgid "page backward"
msgstr ""

#: includes/wcdp-translations.php:266
msgid "page forward"
msgstr ""

#: includes/wcdp-translations.php:267
msgid "closing paren (AZERTY)"
msgstr ""

#: includes/wcdp-translations.php:269
msgid "~ + * key"
msgstr ""

#: includes/wcdp-translations.php:270
msgid "minus (firefox), mute/unmute"
msgstr ""

#: includes/wcdp-translations.php:271
msgid "decrease volume level"
msgstr ""

#: includes/wcdp-translations.php:272
msgid "increase volume level"
msgstr ""

#: includes/wcdp-translations.php:273
msgid "next"
msgstr ""

#: includes/wcdp-translations.php:274
msgid "previous"
msgstr ""

#: includes/wcdp-translations.php:275
msgid "stop"
msgstr ""

#: includes/wcdp-translations.php:276
msgid "play/pause"
msgstr ""

#: includes/wcdp-translations.php:277
msgid "e-mail"
msgstr ""

#: includes/wcdp-translations.php:278
msgid "mute/unmute (firefox)"
msgstr ""

#: includes/wcdp-translations.php:279
msgid "decrease volume level (firefox)"
msgstr ""

#: includes/wcdp-translations.php:280
msgid "increase volume level (firefox)"
msgstr ""

#: includes/wcdp-translations.php:281
msgid "semi-colon / ñ"
msgstr ""

#: includes/wcdp-translations.php:282
msgid "equal sign"
msgstr ""

#: includes/wcdp-translations.php:283
msgid "comma"
msgstr ""

#: includes/wcdp-translations.php:284
msgid "dash"
msgstr ""

#: includes/wcdp-translations.php:285
msgid "period"
msgstr ""

#: includes/wcdp-translations.php:286
msgid "forward slash / ç"
msgstr ""

#: includes/wcdp-translations.php:287
msgid "grave accent / ñ / æ"
msgstr ""

#: includes/wcdp-translations.php:288
msgid "?, / or °"
msgstr ""

#: includes/wcdp-translations.php:289
msgid "numpad period (chrome)"
msgstr ""

#: includes/wcdp-translations.php:290
msgid "open bracket"
msgstr ""

#: includes/wcdp-translations.php:291
msgid "back slash"
msgstr ""

#: includes/wcdp-translations.php:292
msgid "close bracket / å"
msgstr ""

#: includes/wcdp-translations.php:293
msgid "single quote / ø"
msgstr ""

#: includes/wcdp-translations.php:295
msgid "left or right ⌘ key (firefox)"
msgstr ""

#: includes/wcdp-translations.php:298
msgid "GNOME Compose Key"
msgstr ""

#: includes/wcdp-translations.php:300
msgid "XF86Forward"
msgstr ""

#: includes/wcdp-translations.php:301
msgid "XF86Back"
msgstr ""

#: includes/wcdp-translations.php:302
msgid "alphanumeric"
msgstr ""

#: includes/wcdp-translations.php:303
msgid "hiragana/katakana"
msgstr ""

#: includes/wcdp-translations.php:304
msgid "half-width/full-width"
msgstr ""

#: includes/wcdp-translations.php:305
msgid "kanji"
msgstr ""

#: includes/wcdp-translations.php:306
msgid "toggle touchpad"
msgstr ""

#: includes/wcdp-upload-fonts.php:11
msgid "Add fonts"
msgstr ""

#: includes/wcdp-upload-fonts.php:22
msgid "Add google font"
msgstr ""

#: includes/wcdp-upload-fonts.php:23
msgid "Selected fonts"
msgstr ""

#: includes/wcdp-upload-fonts.php:63
msgid "Add custom font"
msgstr ""
