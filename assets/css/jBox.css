
/* Global */

.jBox-wrapper {
  text-align: left;
  box-sizing: border-box;
}

.jBox-title,
.jBox-content,
.jBox-container {
  position: relative;
  word-break: break-word;
  box-sizing: border-box;
}

.jBox-container {
  background: #fff;
}

.jBox-content {
  padding: 8px 10px;
  overflow-x: hidden;
  overflow-y: auto;
  transition: opacity .2s;
}

/* jBox Tooltip */

.jBox-Tooltip .jBox-container,
.jBox-Mouse .jBox-container {
  border-radius: 3px;
  box-shadow: 0 0 3px rgba(0, 0, 0, .25);
}

.jBox-Tooltip .jBox-title,
.jBox-Mouse .jBox-title {
  padding: 8px 10px 0;
  font-weight: bold;
}

.jBox-hasTitle.jBox-Tooltip .jBox-content,
.jBox-hasTitle.jBox-Mouse .jBox-content {
  padding-top: 5px;
}

.jB<PERSON>-<PERSON> {
  pointer-events: none;
}

/* Pointer */

.jBox-pointer {
  position: absolute;
  overflow: hidden;
}

.jBox-pointer-top { top: 0; }
.jBox-pointer-bottom { bottom: 0; }
.jBox-pointer-left { left: 0; }
.jBox-pointer-right { right: 0; }

.jBox-pointer-top,
.jBox-pointer-bottom {
  width: 30px;
  height: 12px;
}

.jBox-pointer-left,
.jBox-pointer-right {
  width: 12px;
  height: 30px;
}

.jBox-pointer:after {
  content: '';
  width: 20px;
  height: 20px;
  position: absolute;
  background: #fff;
  transform: rotate(45deg);
}

.jBox-pointer-top:after {
  left: 5px;
  top: 6px;
  box-shadow: -1px -1px 2px rgba(0, 0, 0, .15);
}

.jBox-pointer-right:after {
  top: 5px;
  right: 6px;
  box-shadow: 1px -1px 2px rgba(0, 0, 0, .15);
}

.jBox-pointer-bottom:after {
  left: 5px;
  bottom: 6px;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, .15);
}

.jBox-pointer-left:after {
  top: 5px;
  left: 6px;
  box-shadow: -1px 1px 2px rgba(0, 0, 0, .15);
}

/* jBox Modal */
.jBox-Modal .jBox-container {
  border-radius: 4px;
}

.jBox-Modal .jBox-content {
  padding: 15px 20px;
  font-size: 14px;
}
.jBox-Modal .jBox-content ul{
   list-style-type: none;
   margin: 0;
   padding: 0;  
}

.jBox-Modal .jBox-title {
  border-radius: 4px 4px 0 0;
  padding: 10px 20px;
  background: #fafafa;
  border-bottom: 1px solid #eee;
}

.jBox-Modal .jBox-footer {
  border-radius: 0 0 4px 4px;
}

.jBox-Modal.jBox-closeButton-title .jBox-title {
  padding-right: 55px;
}

.jBox-Modal .jBox-container,
.jBox-Modal.jBox-closeButton-box:before {
  box-shadow: 0 3px 15px rgba(0, 0, 0, .4), 0 0 5px rgba(0, 0, 0, .4);
}

/* Close button */

.jBox-closeButton {
  cursor: pointer;
  position: absolute;
}

.jBox-closeButton svg {
  position: absolute;
  top: 50%;
  right: 50%;
}

.jBox-closeButton path {
  transition: fill .2s;
}

.jBox-closeButton path {
  fill: #aaa;
}

.jBox-closeButton:hover path {
  fill: #888;
}

.jBox-closeButton:active path {
  fill: #666;
}

/* Close button in overlay */

.jBox-overlay .jBox-closeButton {
  top: 0;
  right: 0;
  width: 40px;
  height: 40px;
}

.jBox-overlay .jBox-closeButton svg {
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-right: -10px;
}

.jBox-overlay .jBox-closeButton path,
.jBox-overlay .jBox-closeButton:active path {
  fill: #ddd;
}

.jBox-overlay .jBox-closeButton:hover path {
  fill: #fff;
}

/* Close button in title */

.jBox-closeButton-title .jBox-closeButton {
  top: 0;
  right: 0;
  bottom: 0;
  width: 40px;
}

.jBox-closeButton-title .jBox-closeButton svg {
  width: 12px;
  height: 12px;
  margin-top: -6px;
  margin-right: -6px;
}

/* Close button in box */

.jBox-closeButton-box .jBox-closeButton {
  top: -8px;
  right: -10px;
  width: 24px;
  height: 24px;
  background: #fff;
  border-radius: 50%;
}

.jBox-closeButton-box .jBox-closeButton svg {
  width: 10px;
  height: 10px;
  margin-top: -5px;
  margin-right: -5px;
}

.jBox-hasTitle.jBox-Modal.jBox-closeButton-box .jBox-closeButton {
  background: #fafafa;
}

.jBox-closeButton-box:before {
  content: '';
  position: absolute;
  top: -8px;
  right: -10px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(0, 0, 0, .3);
}

.jBox-pointerPosition-top.jBox-closeButton-box:before {
  top: 5px;
}

.jBox-pointerPosition-right.jBox-closeButton-box:before {
  right: 2px;
}

/* Overlay */

.jBox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .82);
}

/* Footer */

.jBox-footer {
  background: #fafafa;
  border-top: 1px solid #eee;
  padding: 8px 10px;
  border-radius: 0 0 3px 3px;
}

/* Block scrolling */

body[class^="jBox-blockScroll-"],
body[class*=" jBox-blockScroll-"] {
  overflow: hidden;
}

/* Draggable */

.jBox-draggable {
  cursor: move;
}

/* Spinner */

@keyframes jBoxLoading {
  to { transform: rotate(360deg); }
}

.jBox-loading .jBox-content {
  opacity: .2;
}

.jBox-loading-spinner .jBox-content {
  min-height: 38px !important;
  min-width: 38px !important;
  opacity: 0;
}

.jBox-spinner {
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  margin-top: -12px;
  margin-left: -12px;
}

.jBox-spinner:before {
  display: block;
  box-sizing: border-box;
  content: '';
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid rgba(0, 0, 0, .2);
  border-top-color: rgba(0, 0, 0, .8);
  animation: jBoxLoading .6s linear infinite;
}

/* Animations */

[class^="jBox-animated-"],
[class*=" jBox-animated-"] {
  animation-fill-mode: both;
}

@keyframes jBox-tada {
  0% {transform: scale(1);}
  10%, 20% {transform: scale(0.8) rotate(-4deg);}
  30%, 50%, 70%, 90% {transform: scale(1.2) rotate(4deg);}
  40%, 60%, 80% {transform: scale(1.2) rotate(-4deg);}
  100% {transform: scale(1) rotate(0);}
}

.jBox-animated-tada {
  animation: jBox-tada 1s;
}

@keyframes jBox-tadaSmall {
  0% {transform: scale(1);}
  10%, 20% {transform: scale(0.9) rotate(-2deg);}
  30%, 50%, 70%, 90% {transform: scale(1.1) rotate(2deg);}
  40%, 60%, 80% {transform: scale(1.1) rotate(-2deg);}
  100% {transform: scale(1) rotate(0);}
}

.jBox-animated-tadaSmall {
  animation: jBox-tadaSmall 1s;
}

@keyframes jBox-flash {
  0%, 50%, 100% {opacity: 1;}  
  25%, 75% {opacity: 0;}
}

.jBox-animated-flash {
  animation: jBox-flash .5s;
}

@keyframes jBox-shake {
  0%, 100% {transform: translateX(0);}
  20%, 60% {transform: translateX(-6px);}
  40%, 80% {transform: translateX(6px);}
}

.jBox-animated-shake {
  animation: jBox-shake .4s;
}

@keyframes jBox-pulseUp {
  0% {transform: scale(1);}
  50% {transform: scale(1.15);}
  100% {transform: scale(1);}
}

.jBox-animated-pulseUp {
  animation: jBox-pulseUp .25s;
}

@keyframes jBox-pulseDown {
  0% {transform: scale(1);}
  50% {transform: scale(0.85);}
  100% {transform: scale(1);}
}

.jBox-animated-pulseDown {
  animation: jBox-pulseDown .25s;
}

@keyframes jBox-popIn {
  0% {transform: scale(0);}
  50% {transform: scale(1.1);}
  100% {transform: scale(1);}
}

.jBox-animated-popIn {
  animation: jBox-popIn .25s;
}

@keyframes jBox-popOut {
  0% {transform: scale(1);}
  50% {transform: scale(1.1);}
  100% {transform: scale(0);}
}

.jBox-animated-popOut {
  animation: jBox-popOut .25s;
}

@keyframes jBox-fadeIn {
  0% {opacity: 0;}
  100% {opacity: 1;}
}

.jBox-animated-fadeIn {
  animation: jBox-fadeIn .2s;
}

@keyframes jBox-fadeOut {
  0% {opacity: 1;}
  100% {opacity: 0;}
}

.jBox-animated-fadeOut {
  animation: jBox-fadeOut .2s;
}

@keyframes jBox-slideUp {
  0% {transform: translateY(0);}
  100% {transform: translateY(-300px); opacity: 0;}
}

.jBox-animated-slideUp {
  animation: jBox-slideUp .4s;
}

@keyframes jBox-slideRight {
  0% {transform: translateX(0);}
  100% {transform: translateX(300px); opacity: 0;}
}

.jBox-animated-slideRight {
  animation: jBox-slideRight .4s;
}

@keyframes jBox-slideDown {
  0% {transform: translateY(0);}
  100% {transform: translateY(300px); opacity: 0;}
}

.jBox-animated-slideDown {
  animation: jBox-slideDown .4s;
}

@keyframes jBox-slideLeft {
  0% {transform: translateX(0);}
  100% {transform: translateX(-300px); opacity: 0;}
}

.jBox-animated-slideLeft {
  animation: jBox-slideLeft .4s;
}


.jBox-Confirm .jBox-content {
  text-align: center;
  padding: 46px 35px;
}

.jBox-Confirm-footer {
  height: 46px;
}

.jBox-Confirm-button {
  display: block;
  float: left;
  cursor: pointer;
  text-align: center;
  width: 50%;
  line-height: 46px;
  height: 46px;
  overflow: hidden;
  padding: 0 10px;
  transition: color .2s, background-color .2s;
  box-sizing: border-box;
}

.jBox-Confirm-button-cancel {
  border-bottom-left-radius: 4px;
  background: #ddd;
  color: #666;
}

.jBox-Confirm-button-cancel:hover,
.jBox-Confirm-button-cancel:active {
  background: #ccc;
}

.jBox-Confirm-button-submit {
  border-bottom-right-radius: 4px;
  background: #7d0;
  color: #fff;
}

.jBox-Confirm-button-submit:hover,
.jBox-Confirm-button-submit:active {
  background: #6c0;
}

.jBox-Confirm-button-cancel:active,
.jBox-Confirm-button-submit:active {
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, .2);
}

@media (max-width: 500px) {
  
  .jBox-Confirm .jBox-content {
    padding: 32px 20px;
  }

}

.jBox-Image .jBox-title{
	font-weight: bold;
	text-align: center;
	color: #fff;
}

.jBox-Image .jBox-container {
  background-color: transparent;
}

.jBox-Image .jBox-content {
  padding: 0;
  width: 100%;
  height: 100%;
}

.jBox-image-container {
  background: center center no-repeat;
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 1 !important;
}

.jBox-image-label-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  height: 40px;
  z-index: 100;
}

.jBox-image-label {
  box-sizing: border-box;
  position: absolute;
  bottom: 0;
  width: 100%;
  text-align: center;
  left: 0;
  color: #fff;
  padding: 8px 40px;
  line-height: 24px;
  transition: opacity .36s;
  opacity: 0;
  z-index: 0;
  pointer-events: none;
}

.jBox-image-label.expanded {
  background: #000;
}

.jBox-image-label:not(.expanded) {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.jBox-image-label.active {
  opacity: 1;
  pointer-events: all;
}

.jBox-image-pointer-next,
.jBox-image-pointer-prev {
  position: absolute;
  bottom: 0;
  width: 40px;
  height: 40px;
  cursor: pointer;
  opacity: .8;
  transition: opacity .2s;
  background: no-repeat center center url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ijc0LjcgMjI0IDE4LjcgMzIiPg0KPHBhdGggZmlsbD0iI2ZmZmZmZiIgZD0iTTkzLDIyNy40TDgwLjQsMjQwTDkzLDI1Mi42YzAuNCwwLjQsMC40LDEuMSwwLDEuNWwtMS42LDEuNmMtMC40LDAuNC0xLDAuNS0xLjUsMEw3NSwyNDAuN2MtMC40LTAuNC0wLjUtMSwwLTEuNWwxNC45LTE0LjljMC40LTAuNCwxLTAuNCwxLjUsMGwxLjYsMS42QzkzLjUsMjI2LjQsOTMuNCwyMjcsOTMsMjI3LjR6Ii8+DQo8L3N2Zz4=);
  background-size: 11px auto;
  user-select: none;
  z-index: 1;
}

.jBox-image-pointer-next:hover,
.jBox-image-pointer-prev:hover {
  opacity: 1;
}

.jBox-image-pointer-next {
  right: 0;
  transform: scaleX(-1);
}

.jBox-image-pointer-prev {
  left: 0;
}

.jBox-image-counter-container {
  position: absolute;
  right: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 13px;
  color: #fff;
  text-align: right;
  display: none;
}

.jBox-image-has-counter .jBox-image-counter-container {
  display: block;
}

.jBox-image-has-counter .jBox-image-label:not(.expanded) {
  padding-right: 80px;
  text-indent: 40px;
}

.jBox-overlay.jBox-overlay-Image {
  background: #000;
}

.jBox-image-not-found {
  background: #000;
}

.jBox-image-not-found:before {
  content: '';
  box-sizing: border-box;
  display: block;
  width: 80px;
  height: 80px;
  margin-top: -40px;
  margin-left: -40px;
  position: absolute;
  top: 50%;
  left: 50%;
  border: 5px solid #222;
  border-radius: 50%;
}

.jBox-image-not-found:after {
  content: '';
  display: block;
  box-sizing: content-box;
  z-index: auto;
  width: 6px;
  height: 74px;
  margin-top: -37px;
  margin-left: -3px;
  position: absolute;
  top: 50%;
  left: 50%;
  background: #222;
  transform: rotateZ(45deg);
  transform-origin: 50% 50% 0;
}

/* Image spinner */

@keyframes jBoxImageLoading {
  to { transform: rotate(360deg); }
}

.jBox-image-loading .jBox-container:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  margin-top: -16px;
  margin-left: -16px;
  border: 4px solid #333;
  border-bottom-color: #666;
  animation: jBoxImageLoading 1.2s linear infinite;
  border-radius: 50%;
}


.jBox-Notice {
  transition: margin .2s;
}

.jBox-Notice .jBox-container {
  border-radius: 3px;
  box-shadow: inset 1px 1px 0 0 rgba(255, 255, 255, .25), inset -1px -1px 0 0 rgba(0, 0, 0, .1);
}

.jBox-Notice .jBox-content {
  border-radius: 3px;
  padding: 12px 20px;
}

.jBox-Notice .jBox-title {
  padding: 12px 20px 0;
  font-weight: bold;
}

.jBox-hasTitle.jBox-Notice .jBox-content {
  padding-top: 5px;
}

.jBox-Notice-black .jBox-container {
  color: #fff;
  background: #000;
}

.jBox-Notice-gray .jBox-container {
  color: #333;
  background: #f6f6f6;
}

.jBox-Notice-red .jBox-container {
  color: #fff;
  background: #d00;
}

.jBox-Notice-green .jBox-container {
  color: #fff;
  background: #5d0;
}

.jBox-Notice-blue .jBox-container {
  color: #fff;
  background: #07d;
}

.jBox-Notice-yellow .jBox-container {
  color: #000;
  background: #fd0;
}

@media (max-width: 768px) {
  
  .jBox-Notice .jBox-content {
    padding: 10px 15px;
  }
  
  .jBox-Notice .jBox-title {
    padding: 10px 15px 0;
  }
  
}

@media (max-width: 500px) {
  
  .jBox-Notice .jBox-content {
    padding: 8px 10px;
  }
  
  .jBox-Notice .jBox-title {
    padding: 8px 10px 0;
  }
  
  .jBox-hasTitle.jBox-Notice .jBox-content {
    padding-top: 0;
  }
}
