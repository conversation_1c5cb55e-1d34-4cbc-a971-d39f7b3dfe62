!function(e){"use strict";e(document).ready(function(){function t(t){var n,a=e(t).parent(),s=a.attr("id"),i=a.attr("format"),l=a.attr("support"),d="multiple"==a.attr("multiple");n?n.open():((n=wp.media.frames.file_frame=wp.media({title:wcdp_translations.upload+' "'+l+'"',button:{text:wcdp_translations.file},library:{type:i},multiple:d})).on("select",function(t){n.state().get("selection").map(function(t){var n=(t=t.toJSON()).filename.split(".").pop();if(n&&new RegExp("^("+l.replace(/,/g,"|")+")$").test(n))if(a.hasClass("wcdp-set-img-attr"))(i=a.parent().prev()).find("img").attr("src",t.url),i.find("input").val(t.url),i.find("a").removeClass("dp-disabled");else if("wcdp-media-editor-backend"==s)e.wcdp_upload_file_wp_enqueue_media_backend(t.url,t.title);else if(a.find(".media-filename").length>0)e("#"+s+" .value-file").val(t.url),e("#"+s+" .media-filename").html(t.filename),e("#"+s+" .wcdp-select-file").hide(),e("#"+s+" .wcdp-remove-file").show();else{var i,d=s.slice(9,-8),r="font"==d?"font":"clipart"==d?"clip":"calendar"==d&&"caz";0==(i=e("#wcdp-"+d+"s-contain")).find(".dp-"+r).length&&i.html("");var p='<div class="dp-'+r+'">';if("font"==r){var f=t.title;if(o(f))return!1;e("head").prepend('<style type="text/css">@font-face{\n\tfont-family: '+f+";\n\tsrc: url("+t.url+");\n}\n</style>"),WebFont.load({custom:{families:[f]}}),p+='<p style="font-family:'+f+'">'+f+"</p>",p+='<button class="button wcdp-remove-font">'+wcdp_translations.remove+"</button>"}else if("clip"==r||"caz"==r){var u=t.url;t.sizes&&(u=void 0!==t.sizes.thumbnail?t.sizes.thumbnail.url:void 0!==t.sizes.medium?t.sizes.medium.url:t.sizes.full.url),p+='<span class="dp-img-contain"><img class="lazyload dp-loading-lazy" data-src="'+u+'" src="'+wcdp_lazy_loader+'"/></span>',p+='<div class="wcdp-remove-'+r+'" title="'+wcdp_translations.remove+'"></div>'}p+='<input type="hidden" name="wcdp-uploads-'+d+'s[]" value="'+t.id+'"></div>',i.append(p),c()}else new jBox("Modal",{content:wcdp_translations.unsupported+' "'+l+'"',closeButton:"box",onCloseComplete:function(){this.destroy()}}).open()})}),n.open())}if(c(),e("#wcdp-upload-images-backend, .wcdp-select-file.button").click(function(e){e.preventDefault(),t(this)}),e(".wcdp-remove-file.button").click(function(t){t.preventDefault();var n=e(this).parent().attr("id");e("#"+n+" .value-file").attr("value",""),e("#"+n+" .media-filename").html(""),e("#"+n+" .wcdp-select-file").show(),e("#"+n+" .wcdp-remove-file").hide()}),e("#wcdp-picker-table-update").click(function(t){if(t.preventDefault(),"on"!=wcdp_settings.CMYK)new jBox("Modal",{closeButton:"box",content:wcdp_translations.imagick_disabled,onCloseComplete:function(){this.destroy()}}).open();else{var n=wcdp_settings.profile_rgb,o=wcdp_settings.profile_cmyk;n=n?n.substring(n.lastIndexOf("/")+1):"sRGB-IEC61966-2.1.icc",o=o?o.substring(o.lastIndexOf("/")+1):"ISOcoated_v2_eci.icc",new jBox("Confirm",{id:"jBox-UpdateConfirm",content:wcdp_translations.update_content+":<br><br><b>RGB: "+n+"<br>CMYK: "+o+"</b><br><br><p>"+wcdp_translations.update_info+"</p>",cancelButton:wcdp_translations.cancel,confirmButton:wcdp_translations.confirm,confirm:function(){!function(t,n,o,c,a){var s,i=!0,l="convert",d=function(e,t,n){return new jBox("Modal",{id:"jBox-UpdateTable",title:"<b>"+wcdp_translations.title_update+"</b>",content:'<span class="dp-'+e+'">'+t+"</span>",closeButton:"box",closeOnClick:n,onCloseComplete:function(){i&&(f(),d("error",wcdp_translations.cancel_update,!0),s.abort()),this.destroy()}}).open()},r=d("counter","0%",!1),p=function(){for(var i=0;i<o;i++){var u=t.toString(16);u.length<3&&(u="000".substring(0,3-u.length)+u),c.push("#"+u),t++}t==4096+o&&(c=a,l="update"),s=e.ajax({url:AJAX_URL,type:"POST",data:{action:"wcdp_update_picker_table",colors:c.join(","),mode:l},success:function(e){try{var t=JSON.parse(e);a=a.concat(t),c=[],n=new Number(n)+100/(4096/o),r.setContent('<span class="dp-counter">'+n.toFixed()+"%</span>"),setTimeout(function(){p()},100)}catch(t){f(),"update_successful"==e?d("update",wcdp_translations.update,!0):"error/profile"==e?d("error",wcdp_translations.error_icc,!0):"error/chunk"==e&&d("error",wcdp_translations.error_chunk,!0)}},statusCode:{500:function(){f(),d("error",wcdp_translations.error_icc,!0)}}})},f=function(){i=!1,r.close()};p()}(0,0,parseInt(wcdp_settings.chunk_colors),[],[])},onCloseComplete:function(){this.destroy()}}).open()}}),e("#wcdp-restore-all-defaults").click(function(e){e.preventDefault(),s("restore-defaults")}),e("table.form-table").find(".dp-shortcutkeys").length>0)e(".dp-shortcutkeys").each(function(){var t=e(this).next().val().split("+");1==t.length?e(this).val(wcdp_translations.shortcuts.keyCodes[t[0]]):e(this).val(t[0]+"+"+wcdp_translations.shortcuts.keyCodes[t[1]])}),e(".dp-shortcutkeys").keydown(function(t){var n="",o=!1,c=t.keyCode<16||t.keyCode>18;(t.shiftKey||t.altKey||t.ctrlKey)&&c&&(n=(t.shiftKey?"shift":"")+(t.altKey?"alt":"")+(t.ctrlKey?"ctrl":"")+"+"),t.metaKey||t.preventDefault();var a=n+wcdp_translations.shortcuts.keyCodes[t.keyCode];e(this).val()!=a&&e(".dp-shortcutkeys").each(function(){n+t.keyCode==e(this).next().val()&&(e(".jBox-Modal").remove(),o=!0,new jBox("Modal",{content:"<b>"+a.toUpperCase()+"</b> "+wcdp_translations.shortcuts.key_duplicate,overlay:!1,autoClose:2e3,onCloseComplete:function(){this.destroy()}}).open())}),c&&!o&&e(this).val(a).next().val(n+t.keyCode)});else if(e("table.form-table").find(".wcdp-colors-options").length>0)e.spectrum.installPicker("spectrum-js"),e("#wcdp_option_skin_style").change(function(t){t.preventDefault();for(var n=["color_icons","color_icons_hover","bg_icons","bg_icons_hover","buttons_color","buttons_color_hover","buttons_bg","buttons_bg_hover","buttons_color_jbox","buttons_color_hover_jbox","buttons_bg_jbox","buttons_bg_hover_jbox","buttons_color_folders","buttons_color_folders_select","buttons_color_folders_bg","buttons_color_folders_bg_select","text_color","tabs_bg","tabs_content","tooltip_color","tooltip_bg","scrollbar_bg","border_color","picker_color_bg","picker_color_border","picker_color_text","corner_color","corner_border_color","corner_icons_color"],o={default:["36495e","fff","fff","36495e","36495e","fff","fff","36495e","fff","fff","36495e","bcd800","36495e","36495e","fff","ececec","36495e","fbfbfb","fff","fff","36495e","fff","b6babd","f2f2f2","b6babd","292929","36495e","fff","fff"],"gray-blue":["eee","eee","272c32","00bcd4","eee","eee","272c32","00bcd4","eee","eee","3f4551","00bcd4","eee","eee","3f4551","272c32","eee","3f4551","323844","eee","00bcd4","00bcd4","5a626d","3f4551","323844","eee","272c32","fff","eee"],"green-coral":["eee","eee","063f46","fd7350","eee","eee","063f46","fd7350","eee","eee","065059","fd7350","eee","eee","065059","063f46","eee","065059","06464f","eee","fd7350","fd7350","626d5a","065059","003f47","eee","063f46","fff","eee"],"blue-orange":["eee","eee","36495e","f27e00","eee","eee","36495e","f27e00","eee","eee","36495e","f27e00","eee","eee","446084","36495e","eee","446084","36495e","eee","f27e00","f27e00","738294","446084","36495e","eee","36495e","fff","eee"],"violet-blue":["eee","eee","142443","7e55a7","eee","eee","142443","7e55a7","eee","eee","142443","7e55a7","eee","eee","4d4b9a","142443","eee","4d4b9a","142443","eee","7e55a7","7e55a7","738294","4d4b9a","142443","eee","142443","fff","eee"],"black-red":["eee","eee","1e1e1e","bc1e1e","eee","eee","1e1e1e","bc1e1e","eee","eee","333","bc1e1e","eee","eee","333","1e1e1e","eee","333","484848","eee","bc1e1e","bc1e1e","5a626d","333","484848","eee","1e1e1e","fff","eee"]},c=0;c<n.length;c++)e('[name="wcdp-settings-style['+n[c]+'][RGB]"]').spectrum("set","#"+o[this.value][c]).spectrum("setCMYK","0,0,0,0")}),e(".wcdp-add-color-palette").click(function(t){t.preventDefault();var n="sp_new_color",o='<div class="color">';o+='<input type="text" class="new-spectrum-js '+n+'" name="wcdp-settings-style['+e(this).attr("data")+'][RGB][]" cmyk="0,0,0,0" value="#ffffff">',o+='<input type="hidden" name="wcdp-settings-style['+e(this).attr("data")+'][CMYK][]" value="0,0,0,0">',o+='<button class="button wcdp-remove-color">'+wcdp_translations.remove+"</button>",o+="</div>",e(this).prev().append(o),e.spectrum.installPicker(n),e("."+n).removeClass(n)}),e(".wcdp-colors-palette").on("click",".wcdp-remove-color",function(t){t.preventDefault(),e(this).parent().remove()});else if(e("#wcdp-personalize-product, #wcdp-parameters-box").length>0){function n(t,n){e("#wcdp_tab_product_data").addClass("ld__actv"),e.ajax({url:AJAX_URL,type:"POST",data:{action:"wcdp_manage_attribute_actions_ajax",option:t,product_id:e("#wcdp_product_id").val(),attr_actions:n},success:function(n){(n=JSON.parse(n)).success?"refresh"==t&&n.actions&&e("#wcdp-contain-attributes").empty().append(n.actions):alert(wcdp_translations.error_process),e("#wcdp_tab_product_data").removeClass("ld__actv")}})}e("#wcdp_select_design_id, #wcdp_select_multiple_designs, #_wcdp_design_cat_cliparts, #_wcdp_design_cat_calendars").select2({ajax:{url:AJAX_URL,dataType:"json",delay:250,data:function(t){return{q:t.term,type:e(this).attr("post-id"),action:"wcdp_search_post_ajax"}},processResults:function(t){var n=[];return t&&e.each(t,function(e,t){n.push({id:t[0],text:t[1]})}),{results:n}},cache:!0},placeholder:"",allowClear:!0,minimumInputLength:3}),e("#wcdp_tab_product_data").on("click",".wcdp-expand-all",function(){return e(this).parents(".wc-metaboxes-wrapper").find(".wcdp-attr-item").removeClass("closed").find(".wcdp-item-contain").show(),!1}).on("click",".wcdp-close-all",function(){return e(this).parents(".wc-metaboxes-wrapper").find(".wcdp-attr-item").addClass("closed").find(".wcdp-item-contain").hide(),!1}),e("#wcdp-contain-attributes").on("click","h3",function(t){e(this).parent().toggleClass("closed").find(".wcdp-item-contain").slideToggle("fast")}).on("change",".wcdp-attr-value",function(t){var n=e(this),o=n.parents(".wcdp-item-contain");o.find("table").hide(),o.find('table[data-attr="'+n.val()+'"]').show()}).on("change",'.wcdp-attr-content input[type="color"]',function(t){var n=e(this);n.parent().prev().find("input").val(n.val())}).on("keyup",".wcdp-attr-content input.dp-bg-color",function(t){var n=e(this),o=/^#[0-9A-F]{6}$/i.test(n.val())?n.val():"#000001";n.parent().next().find("input").val(o)}).on("click",".wcdp-attr-content .wcdp-set-img-attr input",function(e){t(this)}).on("click",".wcdp-attr-content a.wcdp-remove-img-action",function(t){var n=e(this),o=n.parent();return o.find("img").attr("src",wcdp_placeholder_img),o.find("input").val(""),n.addClass("dp-disabled"),!1}).on("change",".wcdp-attr-content select.wcdp-set-sides-pr",function(t){var n=e(this);n.next().val(n.val())}),e("#wcdp_save_actions").click(function(t){e("#wcdp_tab_product_data");var o={};e("#wcdp-contain-attributes .wcdp-attr-item").each(function(){for(var t=e(this),n=t.find(".wcdp-attr-name").val(),c=t.find(".wcdp-attr-name").attr("data-slug"),a=t.find(".wcdp-layout-type option:selected").val(),s=t.find(".wcdp-set-img-pr option:selected").val(),i=t.find(".wcdp-attr-value option"),l={},d=0;d<i.length;d++){var r=e(i[d]).val(),p=e('table[data-attr="'+r+'"]').find(".dp-row");l[r]={};for(var f=0;f<p.length;f++){var u=e(p[f]),_=u.attr("data-action"),w=u.find(".dp-col1 input").is(":checked"),m=u.find(".dp-col3 input").val();l[r][_]={active:w?"on":"",value:m}}}o[c]={name:n,layout:a,set_img:s,actions:l}}),Object.keys(o).length>0&&n("save",o)}),e("#wcdp_empty_actions").click(function(t){var n=e("#wcdp-contain-attributes .wcdp-item-contain"),o=n.find(".wcdp-attr-content table");n.find(".dp-col select option:selected").attr("selected",!1),n.find(".dp-col select option:first").attr("selected","selected"),o.find(".dp-col1 input").attr("checked",!1),o.find(".dp-col3 input").val(""),o.find(".dp-col3 img").attr("src",wcdp_placeholder_img),o.find(".dp-col3 a").addClass("dp-disabled"),o.find(".dp-col3 select option:selected").attr("selected",!1),o.find(".dp-col3 select option:first").attr("selected","selected"),o.find('.dp-col4 input[type="color"]').val("#000001"),o.removeAttr("style")}),e("#wcdp_refresh_attributes").click(function(e){n("refresh",!1)}),e("#wcdp-personalize-product input.wcdp-settings-tpl").change(function(t){e(".wcdp-settings-tpl").not(this).attr("checked",!1)})}else e("#wcdp-fonts-contain").length>0&&(e("#wcdp-select-web-fonts").select2(),e("#wcdp-add-web-font").click(function(t){t.preventDefault();var n=e("#wcdp-select-web-fonts"),c=n.find("option:selected").text();o(c)||WebFont.load({google:{families:[c]},active:function(){var t='<div class="dp-font">';t+='<p style="font-family:'+c+'">'+c+"</p>",t+='<button class="button wcdp-remove-font">'+wcdp_translations.remove+"</button>",t+='<input type="hidden" value="'+n.val()+'">',t+="</div>",e("#wcdp-fonts-contain").append(t)}}),0==e("#wcdp-fonts-contain .dp-font").length&&e("#wcdp-fonts-contain").html("")}),e("#wcdp-fonts-contain").on("click",".wcdp-remove-font",function(t){t.preventDefault(),s(e(this).parent())}),e("#wcdp-update-fonts").click(function(t){t.preventDefault();var n=[];e("#wcdp-fonts-contain .dp-font input").each(function(){n.push(e(this).val())}),a({action:"wcdp_update_manage_fonts_options",fonts:n})}));function o(t){var n=0;return e("#wcdp-fonts-contain .dp-font p").each(function(){t==e(this).html()&&(n=!0,new jBox("Modal",{closeButton:"box",content:wcdp_translations.font_selected,onCloseComplete:function(){this.destroy()}}).open())}),n}function c(){new WCDP_LazyLoad({elements_selector:"#wcdp-calendars-contain img.lazyload, #wcdp-cliparts-contain img.lazyload",class_loading:"dp-loading-lazy"})}function a(e){new jBox("Modal",{ajax:{url:AJAX_URL,type:"POST",data:e,reload:"strict",success:function(e){"update_successful"==e?(this.setContent(wcdp_translations.update),location.reload(!0)):this.setContent(wcdp_translations.error_process)}}}).open()}function s(t){new jBox("Confirm",{content:wcdp_translations.confirm_question,cancelButton:wcdp_translations.cancel,confirmButton:wcdp_translations.confirm,confirm:function(){if("restore-defaults"==t)new jBox("Modal",{ajax:{url:AJAX_URL,type:"POST",data:{action:"wcdp_restore_defaults_all_settings"},reload:"strict",success:function(e){"restore_successful"==e?(this.setContent(wcdp_translations.success),location.reload(!0)):this.setContent(wcdp_translations.error_reset)}}}).open();else{var n,o,c=t.attr("class");t.remove(),"dp-font"==c?(n="fonts",o=wcdp_translations.no_fonts):"dp-clip"==c?(n="cliparts",o=wcdp_translations.no_cliparts):"dp-caz"==c&&(n="calendars",o=wcdp_translations.no_caz);var a=e("#wcdp-"+n+"-contain");if(0==a.find("."+c).length){var s='<div id="wcdp-contain-search-empty">';s+='<div class="wcdp-upload-cloud"></div>',s+="<label>"+o+"</label>",s+='<input type="hidden" name="wcdp-uploads-'+n+'">',s+="</div>",a.append(s)}}},onCloseComplete:function(){this.destroy()}}).open()}e("#wcdp-update-shapes").click(function(t){t.preventDefault();var n=[];e("#wcdp-shapes-contain .dp-shap input").each(function(){n.push(e(this).is(":checked")?"on":"off")}),a({action:"wcdp_update_manage_shapes_options",shapes:n})}),e("#wcdp-update-filters").click(function(t){t.preventDefault();var n={};e("#wcdp-filters-contain .dp-filter input").each(function(){var t=e(this),o=t.attr("data-filter");n[o]=t.is(":checked")?"on":"off"}),a({action:"wcdp_update_manage_filters_options",filters:n})}),e("#wcdp-cliparts-contain").on("click",".wcdp-remove-clip",function(t){t.preventDefault(),s(e(this).parent())}),e("#wcdp-calendars-contain").on("click",".wcdp-remove-caz",function(t){t.preventDefault(),s(e(this).parent())}),e("#wcdp-designs-box-editor").parents("form").keypress(function(t){e("input").is(":focus")&&13==t.keyCode&&t.preventDefault()}),e("#wcdp-doc-sidebar > ul > li").click(function(t){t.preventDefault(),0===e(this).length||e(this).hasClass("dp-selected")||(e("#wcdp-doc-sidebar > ul > li").removeClass("dp-selected"),e(this).addClass("dp-selected"),e("#wcdp-doc-content > .dp-doc-section").hide().eq(e(this).index()).fadeIn())}),e("#wcdp-install-all-demos, #wcdp-demos-contain .dp-demo-btn").click(function(t){t.preventDefault();for(var n=wcdp_translations,o=e(this).attr("data-value"),c=wcdp_data_demos.demos,a=wcdp_data_demos.pages,s="alldemos"==o?n.all_demos:c[o],i="<h4>"+n.demo_select+": "+s+"</h4><p>"+n.demo_page+':</p><select id="wcdp-page-demo">',l=0;l<a.length;l++)i+='<option value="'+a[l].id+'">'+a[l].title+"</option>";i+="</select>",new jBox("Confirm",{content:i,cancelButton:n.cancel,confirmButton:n.install,confirm:function(){var t=function(e,t){return new jBox("Modal",{id:"wcdp-jbox-intall-demo",content:e,closeOnEsc:t,closeOnClick:t,closeButton:!!t&&"box",onCloseComplete:function(){this.destroy()}}).open()},a=e("#wcdp-page-demo option:selected").val();if(void 0!==a){var s=t("",!1),i="alldemos"==o?Object.keys(c):[o],l=function(o,d){if(i.length==o)s.close(),t(d,!0);else{var r=i[o];o++,s.setContent(o+"/"+i.length+" "+n.demo_install+": <b>"+c[r]+"</b> "+n.wait+'<div class="install-demo-spinner"></div>'),e.ajax({url:AJAX_URL,type:"POST",data:{action:"wcdp_install_product_demos",pageID:a,demo:r},success:function(e){(e=JSON.parse(e)).err?(s.close(),t(e.err,!0)):e.success?l(o,e.success):(s.close(),t(n.error_process,!0))}})}};l(0,!1)}else t(n.none_demo_page,!0)},onCloseComplete:function(){this.destroy()}}).open()})})}(jQuery);