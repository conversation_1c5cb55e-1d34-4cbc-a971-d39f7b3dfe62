!function(t){"use strict";var e=t.fabric||(t.fabric={}),s=e.util.object.extend,i=e.util.object.clone;if(e.CurvedText)e.warn("fabric.CurvedText is already defined");else{var r=e.Text.prototype.stateProperties.concat();r.push("radius","spacing","reverse","effect","range","largeFont","smallFont");var h=e.Text.prototype._dimensionAffectingProps;h.radius=!0,h.spacing=!0,h.reverse=!0,h.fill=!0,h.effect=!0,h.width=!0,h.height=!0,h.range=!0,h.fontSize=!0,h.shadow=!0,h.largeFont=!0,h.smallFont=!0;var l=e.Group.prototype.delegatedProperties;l.backgroundColor=!0,l.textBackgroundColor=!0,l.textDecoration=!0,l.stroke=!0,l.strokeWidth=!0,l.shadow=!0,l.fontWeight=!0,l.fontStyle=!0,l.strokeWidth=!0,l.textAlign=!0,e.CurvedText=e.util.createClass(e.Text,e.Collection,{type:"curvedText",radius:50,range:5,smallFont:10,largeFont:30,effect:"curved",spacing:20,reverse:!1,stateProperties:r,delegatedProperties:l,_dimensionAffectingProps:h,_isRendering:0,complexity:function(){this.callSuper("complexity")},initialize:function(t,s){s||(s={}),this.letters=new e.Group([],{selectable:!1,padding:0}),this.__skipDimension=!0,this.setOptions(s),this.__skipDimension=!1,this.setText(t)},setText:function(t){if(this.letters){for(;0!==t.length&&this.letters.size()>=t.length;)this.letters.remove(this.letters.item(this.letters.size()-1));for(var s=0;s<t.length;s++)void 0===this.letters.item(s)?this.letters.add(new e.Text(t[s])):this.letters.item(s).setText(t[s])}this.callSuper("setText",t)},_initDimensions:function(t){if(!this.__skipDimension){t||(t=e.util.createCanvasElement().getContext("2d"),this._setTextStyles(t)),this._textLines=this.text.split(this._reNewline),this._clearCache();var s=this.textAlign;this.textAlign="left",this.width=this._getTextWidth(t),this.textAlign=s,this.height=this._getTextHeight(t),this._render(t)}},_render:function(t){var s=e.util.getRandomInt(100,999);if(this._isRendering=s,this.letters){var i=0,r=0,h=0,l=0,n=parseInt(this.spacing),a=0;if("curved"===this.effect){for(var o=0,g=this.text.length;o<g;o++)l+=this.letters.item(o).width+n;l-=n}else"arc"===this.effect&&(a=(parseInt(this.letters.item(0).fontSize)+n)/this.radius/(Math.PI/180),l=(this.text.length+1)*(parseInt(this.letters.item(0).fontSize)+n));i="right"===this.get("textAlign")?90-l/2/this.radius/(Math.PI/180):"left"===this.get("textAlign")?-90-l/2/this.radius/(Math.PI/180):-l/2/this.radius/(Math.PI/180),this.reverse&&(i=-i);var d=0,c=this.reverse?-1:1,f=0,m=0;for(o=0,g=this.text.length;o<g;o++){if(s!==this._isRendering)return;for(var p in this.delegatedProperties)this.letters.item(o).set(p,this.get(p));if(this.letters.item(o).set("left",d),this.letters.item(o).set("top",0),this.letters.item(o).setAngle(0),this.letters.item(o).set("padding",0),"curved"===this.effect)r=c*(c*i+m+(f=(this.letters.item(o).width+n)/this.radius/(Math.PI/180))/4),h=(i=c*(c*i+m))*(Math.PI/180),m=f,this.letters.item(o).setAngle(r),this.letters.item(o).set("top",-1*c*(Math.cos(h)*this.radius)),this.letters.item(o).set("left",c*(Math.sin(h)*this.radius)),this.letters.item(o).set("padding",0),this.letters.item(o).set("selectable",!1);else if("arc"===this.effect)h=(i=c*(c*i+a))*(Math.PI/180),this.letters.item(o).set("top",-1*c*(Math.cos(h)*this.radius)),this.letters.item(o).set("left",c*(Math.sin(h)*this.radius)),this.letters.item(o).set("padding",0),this.letters.item(o).set("selectable",!1);else if("STRAIGHT"===this.effect)this.letters.item(o).set("left",d),this.letters.item(o).set("top",0),this.letters.item(o).setAngle(0),d+=this.letters.item(o).get("width"),this.letters.item(o).set("padding",0),this.letters.item(o).set({borderColor:"red",cornerColor:"green",cornerSize:6,transparentCorners:!1}),this.letters.item(o).set("selectable",!1);else if("smallToLarge"===this.effect){var u=parseInt(this.smallFont),x=(T=parseInt(this.largeFont))-u,v=Math.ceil(this.text.length/2),C=u+o*(_=x/this.text.length);this.letters.item(o).set("fontSize",C),this.letters.item(o).set("left",d),d+=this.letters.item(o).get("width"),this.letters.item(o).set("padding",0),this.letters.item(o).set("selectable",!1),this.letters.item(o).set("top",-1*this.letters.item(o).get("fontSize")+o)}else if("largeToSmallTop"===this.effect){u=parseInt(this.largeFont),x=(T=parseInt(this.smallFont))-u,v=Math.ceil(this.text.length/2),C=u+o*(_=x/this.text.length);this.letters.item(o).set("fontSize",C),this.letters.item(o).set("left",d),d+=this.letters.item(o).get("width"),this.letters.item(o).set("padding",0),this.letters.item(o).set({borderColor:"red",cornerColor:"green",cornerSize:6,transparentCorners:!1}),this.letters.item(o).set("padding",0),this.letters.item(o).set("selectable",!1),this.letters.item(o).top=-1*this.letters.item(o).get("fontSize")+o/this.text.length}else if("largeToSmallBottom"===this.effect){u=parseInt(this.largeFont),x=(T=parseInt(this.smallFont))-u,v=Math.ceil(this.text.length/2),C=u+o*(_=x/this.text.length);this.letters.item(o).set("fontSize",C),this.letters.item(o).set("left",d),d+=this.letters.item(o).get("width"),this.letters.item(o).set("padding",0),this.letters.item(o).set({borderColor:"red",cornerColor:"green",cornerSize:6,transparentCorners:!1}),this.letters.item(o).set("padding",0),this.letters.item(o).set("selectable",!1),this.letters.item(o).top=-1*this.letters.item(o).get("fontSize")-o}else if("bulge"===this.effect){u=parseInt(this.smallFont),x=(T=parseInt(this.largeFont))-u,v=Math.ceil(this.text.length/2);var T,_=x/(this.text.length-v);if(o<v)C=u+o*_;else C=T-(o-v+1)*_;this.letters.item(o).set("fontSize",C),this.letters.item(o).set("left",d),d+=this.letters.item(o).get("width"),this.letters.item(o).set("padding",0),this.letters.item(o).set("selectable",!1),this.letters.item(o).set("top",-1*this.letters.item(o).get("height")/2)}}var b=this.letters.get("scaleX"),S=this.letters.get("scaleY"),w=this.letters.get("angle");this.letters.set("scaleX",1),this.letters.set("scaleY",1),this.letters.set("angle",0),this.letters._calcBounds(),this.letters._updateObjectsCoords(),this.letters.saveCoords(),this.letters.set("scaleX",b),this.letters.set("scaleY",S),this.letters.set("angle",w),this.width=this.letters.width,this.height=this.letters.height,this.letters.left=-this.letters.width/2,this.letters.top=-this.letters.height/2}},_renderOld:function(t){if(this.letters){var e=0,s=0,i=0,r=0;this.reverse&&(r=.5),"center"===this.get("textAlign")||"justify"===this.get("textAlign")?i=this.spacing/2*(this.text.length-r):"right"===this.get("textAlign")&&(i=this.spacing*(this.text.length-r));for(var h=this.reverse?1:-1,l=0,n=this.text.length;l<n;l++){s=(e=h*(-l*parseInt(this.spacing,10)+i))*(Math.PI/180);for(var a in this.delegatedProperties)this.letters.item(l).set(a,this.get(a));this.letters.item(l).set("top",h-Math.cos(s)*this.radius),this.letters.item(l).set("left",h+Math.sin(s)*this.radius),this.letters.item(l).setAngle(e),this.letters.item(l).set("padding",0),this.letters.item(l).set("selectable",!1)}this.letters._calcBounds(),this.reverse?this.letters.top=this.letters.top-2.5*this.height:this.letters.top=0,this.letters.left=this.letters.left-this.width/2,this.letters.saveCoords(),this.width=this.letters.width,this.height=this.letters.height,this.letters.left=-this.letters.width/2,this.letters.top=-this.letters.height/2}},render:function(t,s){if(this.visible&&this.letters){t.save(),this.transform(t);Math.max(this.scaleX,this.scaleY);this.clipTo&&e.util.clipContext(this,t);for(var i=0,r=this.letters.size();i<r;i++){var h=this.letters.item(i);h.borderScaleFactor,h.hasRotatingPoint;h.visible&&h.render(t)}this.clipTo&&t.restore(),t.restore(),this.setCoords()}},_set:function(t,e){this.callSuper("_set",t,e),this.letters&&(this.letters.set(t,e),t in this._dimensionAffectingProps&&(this._initDimensions(),this.setCoords()))},toObject:function(t){var e=s(this.callSuper("toObject",t),{radius:this.radius,spacing:this.spacing,reverse:this.reverse,effect:this.effect,range:this.range,smallFont:this.smallFont,largeFont:this.largeFont});return this.includeDefaultValues||this._removeDefaultValues(e),e},toString:function(){return"#<fabric.CurvedText ("+this.complexity()+'): { "text": "'+this.text+'", "fontFamily": "'+this.fontFamily+'", "radius": "'+this.radius+'", "spacing": "'+this.spacing+'", "reverse": "'+this.reverse+'" }>'},toSVG:function(t){var e=["<g ",'transform="',this.getSvgTransform(),'">'];if(this.letters)for(var s=0,i=this.letters.size();s<i;s++)e.push(this.letters.item(s).toSVG(t));return e.push("</g>"),t?t(e.join("")):e.join("")}}),e.CurvedText.fromObject=function(t){return new e.CurvedText(t.text,i(t))},e.util.createAccessors(e.CurvedText),e.CurvedText.async=!1}}("undefined"!=typeof exports?exports:this);