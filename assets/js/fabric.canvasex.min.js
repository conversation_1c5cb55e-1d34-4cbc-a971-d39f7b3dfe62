﻿!function(){"use strict";var e=fabric.util.addListener,t=fabric.util.removeListener;fabric.Canvas=fabric.util.createClass(fabric.Canvas,{tapholdThreshold:2e3,_bindEvents:function(){var e=this;e.callSuper("_bindEvents"),e._onDoubleClick=e._onDoubleClick.bind(e),e._onTapHold=e._onTapHold.bind(e)},_onDoubleClick:function(e){var t=this,i=t.findRealTarget(e);t.fire("mouse:dblclick",{target:i,e:e}),i&&!t.isDrawingMode&&i.fire("object:dblclick",{e:e})},_onTapHold:function(e){var t=this,i=t.findRealTarget(e);t.fire("touch:taphold",{target:i,e:e}),i&&!t.isDrawingMode&&i.fire("taphold",{e:e}),"touchend"===e.type&&null!=t.touchStartTimer&&clearTimeout(t.touchStartTimer)},_onMouseDown:function(e){var t=this;if(t.callSuper("_onMouseDown",e),"touchstart"!==e.type){var i=!1;if(void 0!==(o=t.findTarget(e))&&void 0!==o._objects&&(i=!0),3===e.which||i&&t.fireEventForObjectInsideGroup){var o=t.findRealTarget(e);i&&t.fireEventForObjectInsideGroup||t.fire("mouse:down",{target:o,e:e}),o&&!t.isDrawingMode&&o.fire("mousedown",{e:e})}}else{var r=setTimeout(function(){t._onTapHold(e),t.isLongTap=!0},t.tapholdThreshold);t.touchStartTimer=r}},_onMouseUp:function(e){var t=this;if(t.callSuper("_onMouseUp",e),"touchend"!==e.type);else{null!=t.touchStartTimer&&clearTimeout(t.touchStartTimer),t.isLongTap&&(t._onLongTapEnd(e),t.isLongTap=!1);var i=(new Date).getTime(),o=i-(t.lastTouch||i+1);o<300&&o>0?(t.lastTouch=null,t._onDoubleTap(e)):t.lastTouch=i}},_onDoubleTap:function(e){var t=this,i=t.findRealTarget(e);t.fire("touch:doubletap",{target:i,e:e}),i&&!t.isDrawingMode&&i.fire("object:doubletap",{e:e})},_onLongTapEnd:function(e){var t=this,i=t.findRealTarget(e);t.fire("touch:longtapend",{target:i,e:e}),i&&!t.isDrawingMode&&i.fire("object:longtapend",{e:e})},_initEventListeners:function(){var t=this;t.callSuper("_initEventListeners"),e(t.upperCanvasEl,"dblclick",t._onDoubleClick)},_checkTargetForGroupObject:function(e,t){if(e&&e.visible&&e.evented&&this._containsPointForGroupObject(t,e)){if(!this.perPixelTargetFind&&!e.perPixelTargetFind||e.isEditing)return!0;if(!this.isTargetTransparent(e,t.x,t.y))return!0}},_containsPointForGroupObject:function(e,t){var i=this._normalizePointer(t,e);return t.containsPoint(i)||t._findTargetCorner(e)},_adjustPointerAccordingToGroupObjects:function(e,t){var i=t._objects,o=i.length;if(o<=0)return e;var r,n=99999,a=99999;for(r=0;r<o;r++){var c=i[r];n>c.left&&(n=c.left),a>c.top&&(a=c.top)}return e.x+=n-t.left,e.y+=a-t.top,e},findRealTarget:function(e){var t,i=this;if(i.fireEventForObjectInsideGroup){if(void 0!==(t=i.findTarget(e,!0))&&void 0!==t._objects){var o=i.getPointer(e,!0),r=t._objects;o=i._adjustPointerAccordingToGroupObjects(o,t);for(var n=r.length;n--;)if(i._checkTargetForGroupObject(r[n],o)){t=r[n];break}}}else t=i.findTarget(e);return t},removeListeners:function(){var e=this;e.callSuper("removeListeners"),t(e.upperCanvasEl,"dblclick",e._onDoubleClick)},fireEventForObjectInsideGroup:!1})}();