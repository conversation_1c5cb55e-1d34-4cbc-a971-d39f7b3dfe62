"use strict";var qrcodegen=new function(){this.QrCode=function(n,o,i,s){if(o<-1||o>7)throw"Mask value out of range";if(i<t||i>e)throw"Version value out of range";for(var f=4*i+17,h=[],u=0;u<f;u++)h.push(!1);var c=[],l=[];for(u=0;u<f;u++)c.push(h.slice()),l.push(h.slice());if(function(){for(var t=0;t<f;t++)p(6,t,t%2==0),p(t,6,t%2==0);m(3,3),m(f-4,3),m(3,f-4);for(var e=r.getAlignmentPatternPositions(i),n=e.length,t=0;t<n;t++)for(var o=0;o<n;o++)0==t&&0==o||0==t&&o==n-1||t==n-1&&0==o||E(e[t],e[o]);d(0),function(){if(i<7)return;for(var t=i,e=0;e<12;e++)t=t<<1^7973*(t>>>11);var r=i<<12|t;if(r>>>18!=0)throw"Assertion error";for(var e=0;e<18;e++){var n=0!=(r>>>e&1),o=f-11+e%3,a=Math.floor(e/3);p(o,a,n),p(a,o,n)}}()}(),function(t){if(t.length!=Math.floor(r.getNumRawDataModules(i)/8))throw"Invalid argument";for(var e=0,n=f-1;n>=1;n-=2){6==n&&(n=5);for(var o=0;o<f;o++)for(var a=0;a<2;a++){var s=n-a,h=0==(n+1&2),u=h?f-1-o:o;!l[u][s]&&e<8*t.length&&(c[u][s]=0!=(t[e>>>3]>>>7-(7&e)&1),e++)}}if(e!=8*t.length)throw"Assertion error"}(function(t){if(t.length!=r.getNumDataCodewords(i,s))throw"Invalid argument";for(var e=r.NUM_ERROR_CORRECTION_BLOCKS[s.ordinal][i],n=r.ECC_CODEWORDS_PER_BLOCK[s.ordinal][i],o=Math.floor(r.getNumRawDataModules(i)/8),f=e-o%e,h=Math.floor(o/e),u=[],c=new a(n),l=0,v=0;l<e;l++){var g=t.slice(v,v+h-n+(l<f?0:1));v+=g.length;var d=c.getRemainder(g);l<f&&g.push(0),d.forEach(function(t){g.push(t)}),u.push(g)}for(var m=[],l=0;l<u[0].length;l++)for(var E=0;E<u.length;E++)(l!=h-n||E>=f)&&m.push(u[E][l]);if(m.length!=o)throw"Assertion error";return m}(n)),-1==o){var v=1/0;for(u=0;u<8;u++){d(u),w(u);var g=C();g<v&&(o=u,v=g),w(u)}}if(o<0||o>7)throw"Assertion error";function d(t){for(var e=s.formatBits<<3|t,r=e,n=0;n<10;n++)r=r<<1^1335*(r>>>9);if(e=e<<10|r,(e^=21522)>>>15!=0)throw"Assertion error";for(n=0;n<=5;n++)p(8,n,0!=(e>>>n&1));p(8,7,0!=(e>>>6&1)),p(8,8,0!=(e>>>7&1)),p(7,8,0!=(e>>>8&1));for(n=9;n<15;n++)p(14-n,8,0!=(e>>>n&1));for(n=0;n<=7;n++)p(f-1-n,8,0!=(e>>>n&1));for(n=8;n<15;n++)p(8,f-15+n,0!=(e>>>n&1));p(8,f-8,!0)}function m(t,e){for(var r=-4;r<=4;r++)for(var n=-4;n<=4;n++){var o=Math.max(Math.abs(r),Math.abs(n)),i=t+n,a=e+r;0<=i&&i<f&&0<=a&&a<f&&p(i,a,2!=o&&4!=o)}}function E(t,e){for(var r=-2;r<=2;r++)for(var n=-2;n<=2;n++)p(t+n,e+r,1!=Math.max(Math.abs(r),Math.abs(n)))}function p(t,e,r){c[e][t]=r,l[e][t]=!0}function w(t){if(t<0||t>7)throw"Mask value out of range";for(var e=0;e<f;e++)for(var r=0;r<f;r++){var n;switch(t){case 0:n=(r+e)%2==0;break;case 1:n=e%2==0;break;case 2:n=r%3==0;break;case 3:n=(r+e)%3==0;break;case 4:n=(Math.floor(r/3)+Math.floor(e/2))%2==0;break;case 5:n=r*e%2+r*e%3==0;break;case 6:n=(r*e%2+r*e%3)%2==0;break;case 7:n=((r+e)%2+r*e%3)%2==0;break;default:throw"Assertion error"}c[e][r]^=n&!l[e][r]}}function C(){for(var t=0,e=0;e<f;e++)for(var n,o,i=0;i<f;i++)0==i||c[e][i]!=o?(o=c[e][i],n=1):5==++n?t+=r.PENALTY_N1:n>5&&t++;for(i=0;i<f;i++){var a,s;for(e=0;e<f;e++)0==e||c[e][i]!=s?(s=c[e][i],a=1):5==++a?t+=r.PENALTY_N1:a>5&&t++}for(e=0;e<f-1;e++)for(i=0;i<f-1;i++){var h=c[e][i];h==c[e][i+1]&&h==c[e+1][i]&&h==c[e+1][i+1]&&(t+=r.PENALTY_N2)}for(e=0;e<f;e++){i=0;for(var u=0;i<f;i++)u=u<<1&2047|(c[e][i]?1:0),i>=10&&(93==u||1488==u)&&(t+=r.PENALTY_N3)}for(i=0;i<f;i++)for(e=0,u=0;e<f;e++)u=u<<1&2047|(c[e][i]?1:0),e>=10&&(93==u||1488==u)&&(t+=r.PENALTY_N3);var l=0;c.forEach(function(t){t.forEach(function(t){t&&l++})});for(var v=f*f,g=0;20*l<(9-g)*v||20*l>(11+g)*v;g++)t+=r.PENALTY_N4;return t}d(o),w(o),Object.defineProperty(this,"version",{value:i}),Object.defineProperty(this,"size",{value:f}),Object.defineProperty(this,"errorCorrectionLevel",{value:s}),Object.defineProperty(this,"mask",{value:o}),this.getModule=function(t,e){return 0<=t&&t<f&&0<=e&&e<f&&c[e][t]},this.isFunctionModule=function(t,e){return 0<=t&&t<f&&0<=e&&e<f&&l[e][t]},this.drawCanvas=function(t,e,r){if(t<=0||e<0)throw"Value out of range";var n=(f+2*e)*t;r.width=n,r.height=n;for(var o=r.getContext("2d"),i=-e;i<f+e;i++)for(var a=-e;a<f+e;a++)o.fillStyle=this.getModule(a,i)?"#000000":"#FFFFFF",o.fillRect((a+e)*t,(i+e)*t,t,t)},this.toSvgString=function(t,e,r){if(t<0)throw"Border must be non-negative";var n='<?xml version="1.0" encoding="UTF-8"?>\n';n+='<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n',n+='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 '+(f+2*t)+" "+(f+2*t)+'" stroke="none">\n',n+='\t<rect width="'+(f+2*t)+'" height="'+(f+2*t)+'" fill="'+r+'"/>\n',n+='\t<path d="';for(var o=!0,i=-t;i<f+t;i++)for(var a=-t;a<f+t;a++)this.getModule(a,i)&&(o?o=!1:n+=" ",n+="M"+(a+t)+","+(i+t)+"h1v1h-1z");return n+='" fill="'+e+'"/>\n',n+="</svg>\n"}},this.QrCode.encodeText=function(t,e){var r=qrcodegen.QrSegment.makeSegments(t);return this.encodeSegments(r,e)},this.QrCode.encodeBinary=function(t,e){var r=qrcodegen.QrSegment.makeBytes(t);return this.encodeSegments([r],e)},this.QrCode.encodeSegments=function(n,o,i,a,f,h){if(void 0==i&&(i=t),void 0==a&&(a=e),void 0==f&&(f=-1),void 0==h&&(h=!0),!(t<=i&&i<=a&&a<=e)||f<-1||f>7)throw"Invalid value";var u,c;for(u=i;;u++){var l=8*r.getNumDataCodewords(u,o);if(null!=(c=qrcodegen.QrSegment.getTotalBits(n,u))&&c<=l)break;if(u>=a)throw"Data too long"}[this.Ecc.MEDIUM,this.Ecc.QUARTILE,this.Ecc.HIGH].forEach(function(t){h&&c<=8*r.getNumDataCodewords(u,t)&&(o=t)});l=8*r.getNumDataCodewords(u,o);var v=new s;n.forEach(function(t){v.appendBits(t.mode.modeBits,4),v.appendBits(t.numChars,t.mode.numCharCountBits(u)),t.getBits().forEach(function(t){v.push(t)})}),v.appendBits(0,Math.min(4,l-v.length)),v.appendBits(0,(8-v.length%8)%8);for(var g=236;v.length<l;g^=253)v.appendBits(g,8);if(v.length%8!=0)throw"Assertion error";return new this(v.getBytes(),f,u,o)};var t=1,e=40;Object.defineProperty(this.QrCode,"MIN_VERSION",{value:t}),Object.defineProperty(this.QrCode,"MAX_VERSION",{value:e});var r={};function n(t,e){Object.defineProperty(this,"ordinal",{value:t}),Object.defineProperty(this,"formatBits",{value:e})}r.getAlignmentPatternPositions=function(r){if(r<t||r>e)throw"Version number out of range";if(1==r)return[];var n,o=4*r+17,i=Math.floor(r/7)+2;n=32!=r?2*Math.ceil((o-13)/(2*i-2)):26;for(var a=[6],s=0,f=o-7;s<i-1;s++,f-=n)a.splice(1,0,f);return a},r.getNumRawDataModules=function(r){if(r<t||r>e)throw"Version number out of range";var n=(16*r+128)*r+64;if(r>=2){var o=Math.floor(r/7)+2;n-=(25*o-10)*o-55,r>=7&&(n-=36)}return n},r.getNumDataCodewords=function(n,o){if(n<t||n>e)throw"Version number out of range";return Math.floor(r.getNumRawDataModules(n)/8)-r.ECC_CODEWORDS_PER_BLOCK[o.ordinal][n]*r.NUM_ERROR_CORRECTION_BLOCKS[o.ordinal][n]},r.PENALTY_N1=3,r.PENALTY_N2=3,r.PENALTY_N3=40,r.PENALTY_N4=10,r.ECC_CODEWORDS_PER_BLOCK=[[null,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[null,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[null,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[null,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],r.NUM_ERROR_CORRECTION_BLOCKS=[[null,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[null,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[null,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[null,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],this.QrCode.Ecc={LOW:new n(0,1),MEDIUM:new n(1,0),QUARTILE:new n(2,3),HIGH:new n(3,2)},this.QrSegment=function(t,e,r){if(e<0||!(t instanceof i))throw"Invalid argument";r=r.slice(),Object.defineProperty(this,"mode",{value:t}),Object.defineProperty(this,"numChars",{value:e}),this.getBits=function(){return r.slice()}},this.QrSegment.makeBytes=function(t){var e=new s;return t.forEach(function(t){e.appendBits(t,8)}),new this(this.Mode.BYTE,t.length,e)},this.QrSegment.makeNumeric=function(t){if(!this.NUMERIC_REGEX.test(t))throw"String contains non-numeric characters";var e,r=new s;for(e=0;e+3<=t.length;e+=3)r.appendBits(parseInt(t.substr(e,3),10),10);var n=t.length-e;return n>0&&r.appendBits(parseInt(t.substring(e),10),3*n+1),new this(this.Mode.NUMERIC,t.length,r)},this.QrSegment.makeAlphanumeric=function(t){if(!this.ALPHANUMERIC_REGEX.test(t))throw"String contains unencodable characters in alphanumeric mode";var e,r=new s;for(e=0;e+2<=t.length;e+=2){var n=45*o.ALPHANUMERIC_CHARSET.indexOf(t.charAt(e));n+=o.ALPHANUMERIC_CHARSET.indexOf(t.charAt(e+1)),r.appendBits(n,11)}return e<t.length&&r.appendBits(o.ALPHANUMERIC_CHARSET.indexOf(t.charAt(e)),6),new this(this.Mode.ALPHANUMERIC,t.length,r)},this.QrSegment.makeSegments=function(t){return""==t?[]:this.NUMERIC_REGEX.test(t)?[this.makeNumeric(t)]:this.ALPHANUMERIC_REGEX.test(t)?[this.makeAlphanumeric(t)]:[this.makeBytes(function(t){t=encodeURI(t);for(var e=[],r=0;r<t.length;r++)"%"!=t.charAt(r)?e.push(t.charCodeAt(r)):(e.push(parseInt(t.substr(r+1,2),16)),r+=2);return e}(t))]},this.QrSegment.makeEci=function(t){var e=new s;if(0<=t&&t<128)e.appendBits(t,8);else if(128<=t&&t<16384)e.appendBits(2,2),e.appendBits(t,14);else{if(!(16384<=t&&t<1e6))throw"ECI assignment value out of range";e.appendBits(6,3),e.appendBits(t,21)}return new this(this.Mode.ECI,0,e)},this.QrSegment.getTotalBits=function(r,n){if(n<t||n>e)throw"Version number out of range";for(var o=0,i=0;i<r.length;i++){var a=r[i],s=a.mode.numCharCountBits(n);if(a.numChars>=1<<s)return null;o+=4+s+a.getBits().length}return o};var o={};function i(t,e){Object.defineProperty(this,"modeBits",{value:t}),this.numCharCountBits=function(t){if(1<=t&&t<=9)return e[0];if(10<=t&&t<=26)return e[1];if(27<=t&&t<=40)return e[2];throw"Version number out of range"}}function a(t){if(t<1||t>255)throw"Degree out of range";for(var e=[],r=0;r<t-1;r++)e.push(0);e.push(1);var n=1;for(r=0;r<t;r++){for(var o=0;o<e.length;o++)e[o]=a.multiply(e[o],n),o+1<e.length&&(e[o]^=e[o+1]);n=a.multiply(n,2)}this.getRemainder=function(t){var r=e.map(function(){return 0});return t.forEach(function(t){var n=t^r.shift();r.push(0);for(var o=0;o<r.length;o++)r[o]^=a.multiply(e[o],n)}),r}}function s(){this.getBytes=function(){for(var t=[];8*t.length<this.length;)t.push(0);return this.forEach(function(e,r){t[r>>>3]|=e<<7-(7&r)}),t},this.appendBits=function(t,e){if(e<0||e>31||t>>>e!=0)throw"Value out of range";for(var r=e-1;r>=0;r--)this.push(t>>>r&1)}}this.QrSegment.NUMERIC_REGEX=/^[0-9]*$/,this.QrSegment.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,o.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",this.QrSegment.Mode={NUMERIC:new i(1,[10,12,14]),ALPHANUMERIC:new i(2,[9,11,13]),BYTE:new i(4,[8,16,16]),KANJI:new i(8,[8,10,12]),ECI:new i(7,[0,0,0])},a.multiply=function(t,e){if(t>>>8!=0||e>>>8!=0)throw"Byte out of range";for(var r=0,n=7;n>=0;n--)r=r<<1^285*(r>>>7),r^=(e>>>n&1)*t;if(r>>>8!=0)throw"Assertion error";return r},s.prototype=Object.create(Array.prototype)};