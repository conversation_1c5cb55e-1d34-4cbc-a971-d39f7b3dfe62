var SVGtoPDF=function(t,e,i,n,s){"use strict";const a={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanche<PERSON>mond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgrey:[169,169,169],darkgreen:[0,100,0],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgrey:[211,211,211],lightgreen:[144,238,144],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0]},r={black:[a.black,1],white:[a.white,1],transparent:[a.black,0]},h={quot:34,amp:38,lt:60,gt:62,apos:39,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,circ:710,tilde:732,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,permil:8240,lsaquo:8249,rsaquo:8250,euro:8364,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,copy:169,ordf:170,laquo:171,not:172,shy:173,reg:174,macr:175,deg:176,plusmn:177,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,sup1:185,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,Agrave:192,Aacute:193,Acirc:194,Atilde:195,Auml:196,Aring:197,AElig:198,Ccedil:199,Egrave:200,Eacute:201,Ecirc:202,Euml:203,Igrave:204,Iacute:205,Icirc:206,Iuml:207,ETH:208,Ntilde:209,Ograve:210,Oacute:211,Ocirc:212,Otilde:213,Ouml:214,times:215,Oslash:216,Ugrave:217,Uacute:218,Ucirc:219,Uuml:220,Yacute:221,THORN:222,szlig:223,agrave:224,aacute:225,acirc:226,atilde:227,auml:228,aring:229,aelig:230,ccedil:231,egrave:232,eacute:233,ecirc:234,euml:235,igrave:236,iacute:237,icirc:238,iuml:239,eth:240,ntilde:241,ograve:242,oacute:243,ocirc:244,otilde:245,ouml:246,divide:247,oslash:248,ugrave:249,uacute:250,ucirc:251,uuml:252,yacute:253,thorn:254,yuml:255,fnof:402,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,bull:8226,hellip:8230,prime:8242,Prime:8243,oline:8254,frasl:8260,weierp:8472,image:8465,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},o={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0},l={A3:!0,A4:!0,a3:!0,a4:!0},c={color:{inherit:!0,initial:void 0},visibility:{inherit:!0,initial:"visible",values:{hidden:"hidden",collapse:"hidden",visible:"visible"}},fill:{inherit:!0,initial:r.black},stroke:{inherit:!0,initial:"none"},"stop-color":{inherit:!1,initial:r.black},"fill-opacity":{inherit:!0,initial:1},"stroke-opacity":{inherit:!0,initial:1},"stop-opacity":{inherit:!1,initial:1},"fill-rule":{inherit:!0,initial:"nonzero",values:{nonzero:"nonzero",evenodd:"evenodd"}},"clip-rule":{inherit:!0,initial:"nonzero",values:{nonzero:"nonzero",evenodd:"evenodd"}},"stroke-width":{inherit:!0,initial:1},"stroke-dasharray":{inherit:!0,initial:[]},"stroke-dashoffset":{inherit:!0,initial:0},"stroke-miterlimit":{inherit:!0,initial:4},"stroke-linejoin":{inherit:!0,initial:"miter",values:{miter:"miter",round:"round",bevel:"bevel"}},"stroke-linecap":{inherit:!0,initial:"butt",values:{butt:"butt",round:"round",square:"square"}},"font-size":{inherit:!0,initial:16,values:{"xx-small":9,"x-small":10,small:13,medium:16,large:18,"x-large":24,"xx-large":32}},"font-family":{inherit:!0,initial:"sans-serif"},"font-weight":{inherit:!0,initial:"normal",values:{600:"bold",700:"bold",800:"bold",900:"bold",bold:"bold",bolder:"bold",500:"normal",400:"normal",300:"normal",200:"normal",100:"normal",normal:"normal",lighter:"normal"}},"font-style":{inherit:!0,initial:"normal",values:{italic:"italic",oblique:"italic",normal:"normal"}},"text-anchor":{inherit:!0,initial:"start",values:{start:"start",middle:"middle",end:"end"}},direction:{inherit:!0,initial:"ltr",values:{ltr:"ltr",rtl:"rtl"}},"dominant-baseline":{inherit:!0,initial:"baseline",values:{auto:"baseline",baseline:"baseline","before-edge":"before-edge","text-before-edge":"before-edge",middle:"middle",central:"central","after-edge":"after-edge","text-after-edge":"after-edge",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"mathematical"}},"alignment-baseline":{inherit:!1,initial:void 0,values:{auto:"baseline",baseline:"baseline","before-edge":"before-edge","text-before-edge":"before-edge",middle:"middle",central:"central","after-edge":"after-edge","text-after-edge":"after-edge",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"mathematical"}},"baseline-shift":{inherit:!0,initial:"baseline",values:{baseline:"baseline",sub:"sub",super:"super"}},"word-spacing":{inherit:!0,initial:0,values:{normal:0}},"letter-spacing":{inherit:!0,initial:0,values:{normal:0}},"text-decoration":{inherit:!1,initial:"none",values:{none:"none",underline:"underline",overline:"overline","line-through":"line-through"}},"xml:space":{inherit:!0,initial:"default",css:"white-space",values:{preserve:"preserve",default:"default",pre:"preserve","pre-line":"preserve","pre-wrap":"preserve",nowrap:"default"}},"marker-start":{inherit:!0,initial:"none"},"marker-mid":{inherit:!0,initial:"none"},"marker-end":{inherit:!0,initial:"none"},opacity:{inherit:!1,initial:1},transform:{inherit:!1,initial:[1,0,0,1,0,0]},display:{inherit:!1,initial:"inline",values:{none:"none",inline:"inline",block:"inline"}},"clip-path":{inherit:!1,initial:"none"},mask:{inherit:!1,initial:"none"},overflow:{inherit:!1,initial:"hidden",values:{hidden:"hidden",scroll:"hidden",visible:"visible"}}};function g(e){let i=new function(){};return i.name="G"+(t._groupCount=(t._groupCount||0)+1),i.resources=t.ref(),i.xobj=t.ref({Type:"XObject",Subtype:"Form",FormType:1,BBox:e,Group:{S:"Transparency",CS:"DeviceRGB",I:!0,K:!1},Resources:i.resources}),i.xobj.write(""),i.savedMatrix=t._ctm,i.savedPage=t.page,Ht.push(i),t._ctm=[1,0,0,1,0,0],t.page={width:t.page.width,height:t.page.height,write:function(t){i.xobj.write(t)},fonts:{},xobjects:{},ext_gstates:{},patterns:{}},i}function u(e){if(e!==Ht.pop())throw"Group not matching";Object.keys(t.page.fonts).length&&(e.resources.data.Font=t.page.fonts),Object.keys(t.page.xobjects).length&&(e.resources.data.XObject=t.page.xobjects),Object.keys(t.page.ext_gstates).length&&(e.resources.data.ExtGState=t.page.ext_gstates),Object.keys(t.page.patterns).length&&(e.resources.data.Pattern=t.page.patterns),e.resources.end(),e.xobj.end(),t._ctm=e.savedMatrix,t.page=e.savedPage}function d(e){t.page.xobjects[e.name]=e.xobj,t.addContent("/"+e.name+" Do")}function f(e,i){let n="M"+(t._maskCount=(t._maskCount||0)+1),s=t.ref({Type:"ExtGState",CA:1,ca:1,BM:"Normal",SMask:{S:"Luminosity",G:e.xobj,BC:i?[0,0,0]:[1,1,1]}});s.end(),t.page.ext_gstates[n]=s,t.addContent("/"+n+" gs")}function p(t,e,i,n){let s=new function(){};return s.group=t,s.dx=e,s.dy=i,s.matrix=n||[1,0,0,1,0,0],s}function m(e,i){let n="P"+(t._patternCount=(t._patternCount||0)+1),s=t.ref({Type:"Pattern",PatternType:1,PaintType:1,TilingType:2,BBox:[0,0,e.dx,e.dy],XStep:e.dx,YStep:e.dy,Matrix:L(t._ctm,e.matrix),Resources:{ProcSet:["PDF","Text","ImageB","ImageC","ImageI"],XObject:function(){let t={};return t[e.group.name]=e.group.xobj,t}()}});s.write("/"+e.group.name+" Do"),s.end(),t.page.patterns[n]=s,i?(t.addContent("/Pattern CS"),t.addContent("/"+n+" SCN")):(t.addContent("/Pattern cs"),t.addContent("/"+n+" scn"))}function b(e,i){let n=e&&i?2:i?1:e?0:3;t.addContent(n+" Tr")}function w(e){"PDFPattern"===e[0].constructor.name?(t.fillOpacity(e[1]),m(e[0],!1)):t.fillColor(e[0],e[1])}function y(e){"PDFPattern"===e[0].constructor.name?(t.strokeOpacity(e[1]),m(e[0],!0)):t.strokeColor(e[0],e[1])}function x(t){let e=function(t,e,i,n){this.error=n,this.nodeName=t,this.nodeValue=i,this.nodeType=e,this.attributes=Object.create(null),this.childNodes=[],this.parentNode=null,this.id="",this.textContent="",this.classList=[]};e.prototype.getAttribute=function(t){return null!=this.attributes[t]?this.attributes[t]:null},e.prototype.getElementById=function(t){let e=null;return function i(n){if(!e&&1===n.nodeType){n.id===t&&(e=n);for(let t=0;t<n.childNodes.length;t++)i(n.childNodes[t])}}(this),e},e.prototype.getElementsByTagName=function(t){let e=[];return function i(n){if(1===n.nodeType){n.nodeName===t&&e.push(n);for(let t=0;t<n.childNodes.length;t++)i(n.childNodes[t])}}(this),e};let i,n,s=new R(t.trim()),a=!1,r=function(){let t,i;if(t=s.match(/^<([\w:.-]+)\s*/,!0)){let n=new e(t[1],1,null,a);for(;t=s.match(/^([\w:.-]+)(?:\s*=\s*"([^"]*)"|\s*=\s*'([^']*)')?\s*/,!0);){let e=t[1],i=k(t[2]||t[3]||"");n.attributes[e]?(St('parseXml: duplicate attribute "'+e+'"'),a=!0):(n.attributes[e]=i,"id"===e&&(n.id=i),"class"===e&&(n.classList=i.split(" ")))}if(s.match(/^>/)){for(;i=r();)n.childNodes.push(i),i.parentNode=n,n.textContent+=3===i.nodeType||4===i.nodeType?i.nodeValue:i.textContent;return(t=s.match(/^<\/([\w:.-]+)\s*>/,!0))?t[1]===n.nodeName?n:(St('parseXml: tag not matching, opening "'+n.nodeName+'" & closing "'+t[1]+'"'),a=!0,n):(St('parseXml: tag not matching, opening "'+n.nodeName+'" & not closing'),a=!0,n)}if(s.match(/^\/>/))return n;St('parseXml: tag could not be parsed "'+n.nodeName+'"'),a=!0}else{if(t=s.match(/^<!--[\s\S]*?-->/))return new e(null,8,t,a);if(t=s.match(/^<\?[\s\S]*?\?>/))return new e(null,7,t,a);if(t=s.match(/^<!DOCTYPE\s*([\s\S]*?)>/))return new e(null,10,t,a);if(t=s.match(/^<!\[CDATA\[([\s\S]*?)\]\]>/,!0))return new e("#cdata-section",4,t[1],a);if(t=s.match(/^([^<]+)/,!0))return new e("#text",3,k(t[1]),a)}};for(;n=r();)1!==n.nodeType||i?(1===n.nodeType||3===n.nodeType&&""!==n.nodeValue.trim())&&St("parseXml: data after document end has been discarded"):i=n;return s.matchAll()&&St("parseXml: parsing error"),i}function k(t){return t.replace(/&(?:#([0-9]+)|#[xX]([0-9A-Fa-f]+)|([0-9A-Za-z]+));/g,function(t,e,i,n){return e?String.fromCharCode(parseInt(e,10)):i?String.fromCharCode(parseInt(i,16)):n&&h[n]?String.fromCharCode(h[n]):t})}function M(t){let e,i;return t=(t||"").trim(),(e=a[t])?i=[e.slice(),1]:(e=t.match(/^rgba\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9.]+)\s*\)$/i))?(e[1]=parseInt(e[1]),e[2]=parseInt(e[2]),e[3]=parseInt(e[3]),e[4]=parseFloat(e[4]),e[1]<256&&e[2]<256&&e[3]<256&&e[4]<=1&&(i=[e.slice(1,4),e[4]])):(e=t.match(/^rgb\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)$/i))?(e[1]=parseInt(e[1]),e[2]=parseInt(e[2]),e[3]=parseInt(e[3]),e[1]<256&&e[2]<256&&e[3]<256&&(i=[e.slice(1,4),1])):(e=t.match(/^rgb\(\s*([0-9.]+)%\s*,\s*([0-9.]+)%\s*,\s*([0-9.]+)%\s*\)$/i))?(e[1]=2.55*parseFloat(e[1]),e[2]=2.55*parseFloat(e[2]),e[3]=2.55*parseFloat(e[3]),e[1]<256&&e[2]<256&&e[3]<256&&(i=[e.slice(1,4),1])):(e=t.match(/^#([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})$/i))?i=[[parseInt(e[1],16),parseInt(e[2],16),parseInt(e[3],16)],1]:(e=t.match(/^#([0-9a-f])([0-9a-f])([0-9a-f])$/i))&&(i=[[17*parseInt(e[1],16),17*parseInt(e[2],16),17*parseInt(e[3],16)],1]),It?It(i,t):i}function v(t,e,i){let n=t[0].slice(),s=t[1]*e;if(i){for(let e=0;e<t.length;e++)n[e]*=s;return[n,1]}return[n,s]}function L(){let t=arguments[0];for(let n=1;n<arguments.length;n++)e=t,i=arguments[n],t=[e[0]*i[0]+e[2]*i[1],e[1]*i[0]+e[3]*i[1],e[0]*i[2]+e[2]*i[3],e[1]*i[2]+e[3]*i[3],e[0]*i[4]+e[2]*i[5]+e[4],e[1]*i[4]+e[3]*i[5]+e[5]];var e,i;return t}function P(t,e){return[e[0]*t[0]+e[2]*t[1]+e[4],e[1]*t[0]+e[3]*t[1]+e[5]]}function V(){let e=t._ctm;for(let t=Ht.length-1;t>=0;t--)e=L(Ht[t].savedMatrix,e);return e}function C(){return(new X).M(0,0).L(t.page.width,0).L(t.page.width,t.page.height).L(0,t.page.height).transform(_(V())).getBoundingBox()}function _(t){let e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[3]*t[4])/e,(t[1]*t[4]-t[0]*t[5])/e]}function S(t){let e=T(t[0]),i=T(t[1]),n=T(t[2]),s=T(t[3]),a=T(t[4]),r=T(t[5]);if(O(e*s-i*n,0))return[e,i,n,s,a,r]}function B(t){let e=t[2]||0,i=t[1]||0,n=t[0]||0;if(I(e,0)&&I(i,0))return[];if(I(e,0))return[-n/i];{let t=i*i-4*e*n;return O(t,0)&&t>0?[(-i+Math.sqrt(t))/(2*e),(-i-Math.sqrt(t))/(2*e)]:I(t,0)?[-i/(2*e)]:[]}}function A(t,e){return(e[0]||0)+(e[1]||0)*t+(e[2]||0)*t*t+(e[3]||0)*t*t*t}function I(t,e){return Math.abs(t-e)<1e-10}function O(t,e){return Math.abs(t-e)>=1e-10}function T(t){return t>-1e21&&t<1e21?Math.round(1e6*t)/1e6:0}function H(t){let e,i=new R((t||"").trim()),n=[1,0,0,1,0,0];for(;e=i.match(/^([A-Za-z]+)\s*[(]([^(]+)[)]/,!0);){let t,s=e[1],a=[],r=new R(e[2].trim());for(;t=r.matchNumber();)a.push(Number(t)),r.matchSeparator();if("matrix"===s&&6===a.length)n=L(n,[a[0],a[1],a[2],a[3],a[4],a[5]]);else if("translate"===s&&2===a.length)n=L(n,[1,0,0,1,a[0],a[1]]);else if("translate"===s&&1===a.length)n=L(n,[1,0,0,1,a[0],0]);else if("scale"===s&&2===a.length)n=L(n,[a[0],0,0,a[1],0,0]);else if("scale"===s&&1===a.length)n=L(n,[a[0],0,0,a[0],0,0]);else if("rotate"===s&&3===a.length){let t=a[0]*Math.PI/180;n=L(n,[1,0,0,1,a[1],a[2]],[Math.cos(t),Math.sin(t),-Math.sin(t),Math.cos(t),0,0],[1,0,0,1,-a[1],-a[2]])}else if("rotate"===s&&1===a.length){let t=a[0]*Math.PI/180;n=L(n,[Math.cos(t),Math.sin(t),-Math.sin(t),Math.cos(t),0,0])}else if("skewX"===s&&1===a.length){let t=a[0]*Math.PI/180;n=L(n,[1,0,Math.tan(t),1,0,0])}else{if("skewY"!==s||1!==a.length)return;{let t=a[0]*Math.PI/180;n=L(n,[1,Math.tan(t),0,1,0,0])}}i.matchSeparator()}if(!i.matchAll())return n}function N(t,e,i,n,s,a){let r=(t||"").trim().match(/^(none)$|^x(Min|Mid|Max)Y(Min|Mid|Max)(?:\s+(meet|slice))?$/)||[],h=r[1]||r[4]||"meet",o=r[2]||"Mid",l=r[3]||"Mid",c=e/n,g=i/s,u={Min:0,Mid:.5,Max:1}[o]-(a||0),d={Min:0,Mid:.5,Max:1}[l]-(a||0);return"slice"===h?g=c=Math.max(c,g):"meet"===h&&(g=c=Math.min(c,g)),[c,0,0,g,u*(e-n*c),d*(i-s*g)]}function W(t){let e=Object.create(null);t=(t||"").trim().split(/;/);for(let i=0;i<t.length;i++){let n=(t[i].split(":")[0]||"").trim(),s=(t[i].split(":")[1]||"").trim();n&&(e[n]=s)}if(e.marker&&(e["marker-start"]||(e["marker-start"]=e.marker),e["marker-mid"]||(e["marker-mid"]=e.marker),e["marker-end"]||(e["marker-end"]=e.marker)),e.font){let t=null,i=null,n="normal",s="normal",a="normal",r=e.font.split(/\s+/);for(let e=0;e<r.length;e++)switch(r[e]){case"normal":break;case"italic":case"oblique":n=r[e];break;case"small-caps":a=r[e];break;case"bold":case"bolder":case"lighter":case"100":case"200":case"300":case"400":case"500":case"600":case"700":case"800":case"900":s=r[e];break;default:i?t?t+=" "+r[e]:t=r[e]:i=r[e].split("/")[0]}e["font-style"]||(e["font-style"]=n),e["font-variant"]||(e["font-variant"]=a),e["font-weight"]||(e["font-weight"]=s),e["font-size"]||(e["font-size"]=i),e["font-family"]||(e["font-family"]=t)}return e}function q(t){let e,i=t.split(/(?=[.#])/g),n=[],s=[],a=[];for(let t=0;t<i.length;t++)if(e=i[t].match(/^[#]([_A-Za-z0-9-]+)$/))n.push(e[1]);else if(e=i[t].match(/^[.]([_A-Za-z0-9-]+)$/))s.push(e[1]);else if(e=i[t].match(/^([_A-Za-z0-9-]+)$/))a.push(e[1]);else if("*"!==i[t])return;return{tags:a,ids:n,classes:s,specificity:1e4*n.length+100*s.length+a.length}}function z(t){let e,i=new R(t.trim()),n=[];for(;e=i.match(/^\s*([^\{\}]*?)\s*\{([^\{\}]*?)\}/,!0);){let t=e[1].split(/\s*,\s*/g),i=W(e[2]);for(let e=0;e<t.length;e++){let s=q(t[e]);s&&n.push({selector:s,css:i})}}return n}function j(t,e){if(1!==t.nodeType)return!1;for(let i=0;i<e.tags.length;i++)if(e.tags[i]!==t.nodeName)return!1;for(let i=0;i<e.ids.length;i++)if(e.ids[i]!==t.id)return!1;for(let i=0;i<e.classes.length;i++)if(-1===t.classList.indexOf(e.classes[i]))return!1;return!0}function U(t,e){return t.concat(e.slice(t.length))}function D(t,e){return Math.max(t.ascender,(t.bbox[3]||t.bbox.maxY)*(t.scale||1))*e/1e3}function F(t,e){return Math.min(t.descender,(t.bbox[1]||t.bbox.minY)*(t.scale||1))*e/1e3}function E(t,e,i){let n=t.encode(""+i),s=n[0],a=n[1],r=[];for(let n=0;n<s.length;n++){let h=t.unicode?t.unicode[parseInt(s[n],16)]:[i.charCodeAt(n)];r.push({glyph:s[n],unicode:h,width:a[n].advanceWidth*e/1e3,xOffset:a[n].xOffset*e/1e3,yOffset:a[n].yOffset*e/1e3,xAdvance:a[n].xAdvance*e/1e3,yAdvance:a[n].yAdvance*e/1e3})}return r}function G(t,e){switch(t.nodeName){case"use":return new tt(t,e);case"symbol":return new et(t,e);case"g":return new it(t,e);case"a":return new nt(t,e);case"svg":return new st(t,e);case"image":return new at(t,e);case"rect":return new lt(t,e);case"circle":return new ct(t,e);case"ellipse":return new gt(t,e);case"line":return new ut(t,e);case"polyline":return new dt(t,e);case"polygon":return new ft(t,e);case"path":return new pt(t,e);case"text":return new vt(t,e);case"tspan":return new kt(t,e);case"textPath":return new Mt(t,e);case"#text":case"#cdata-section":return new xt(t,e);default:return new Q(t,e)}}var R=function(t){this.match=function(e,i){let n=t.match(e);if(n&&0===n.index)return t=t.substring(n[0].length),i?n:n[0]},this.matchSeparator=function(){return this.match(/^(?:\s*,\s*|\s*|)/)},this.matchSpace=function(){return this.match(/^(?:\s*)/)},this.matchLengthUnit=function(){return this.match(/^(?:px|pt|cm|mm|in|pc|em|ex|%|)/)},this.matchNumber=function(){return this.match(/^(?:[-+]?(?:[0-9]+[.][0-9]+|[0-9]+[.]|[.][0-9]+|[0-9]+)(?:[eE][-+]?[0-9]+)?)/)},this.matchAll=function(){return this.match(/^[\s\S]+/)}},$=function(t,e,i,n,s,a,r,h){let o=6*Tt,l=[t,-3*t+3*i,3*t-6*i+3*s,3*i-t-3*s+r],c=[e,-3*e+3*n,3*e-6*n+3*a,3*n-e-3*a+h],g=[-3*t+3*i,6*t-12*i+6*s,-3*t+9*i-9*s+3*r],u=[-3*e+3*n,6*e-12*n+6*a,-3*e+9*n-9*a+3*h],d=[0];for(let t=1;t<=o;t++){let e=(t-.5)/o,i=A(e,g)/o,n=A(e,u)/o,s=Math.sqrt(i*i+n*n);d[t]=d[t-1]+s}this.totalLength=d[o],this.startPoint=[t,e,I(t,i)&&I(e,n)?Math.atan2(a-n,s-i):Math.atan2(n-e,i-t)],this.endPoint=[r,h,I(s,r)&&I(a,h)?Math.atan2(a-n,s-i):Math.atan2(h-a,r-s)],this.getBoundingBox=function(){let t,e=A(0,l),i=A(0,c),n=A(1,l),s=A(1,c);e>n&&(t=n,n=e,e=t),i>s&&(t=s,s=i,i=t);let a=B(g);for(let t=0;t<a.length;t++)if(a[t]>=0&&a[t]<=1){let i=A(a[t],l);i<e&&(e=i),i>n&&(n=i)}let r=B(u);for(let t=0;t<r.length;t++)if(r[t]>=0&&r[t]<=1){let e=A(r[t],c);e<i&&(i=e),e>s&&(s=e)}return[e,i,n,s]},this.getPointAtLength=function(t){if(I(t,0))return this.startPoint;if(I(t,this.totalLength))return this.endPoint;if(!(t<0||t>this.totalLength))for(let e=1;e<=o;e++){let i=d[e-1],n=d[e];if(i<=t&&t<=n){let s=(e-(n-t)/(n-i))/o,a=A(s,l),r=A(s,c),h=A(s,g),d=A(s,u);return[a,r,Math.atan2(d,h)]}}}},Z=function(t,e,i,n){this.totalLength=Math.sqrt((i-t)*(i-t)+(n-e)*(n-e)),this.startPoint=[t,e,Math.atan2(n-e,i-t)],this.endPoint=[i,n,Math.atan2(n-e,i-t)],this.getBoundingBox=function(){return[Math.min(this.startPoint[0],this.endPoint[0]),Math.min(this.startPoint[1],this.endPoint[1]),Math.max(this.startPoint[0],this.endPoint[0]),Math.max(this.startPoint[1],this.endPoint[1])]},this.getPointAtLength=function(t){if(t>=0&&t<=this.totalLength){let e=t/this.totalLength||0;return[this.startPoint[0]+e*(this.endPoint[0]-this.startPoint[0]),this.startPoint[1]+e*(this.endPoint[1]-this.startPoint[1]),this.startPoint[2]]}}},X=function(){this.pathCommands=[],this.pathSegments=[],this.startPoint=null,this.endPoint=null,this.totalLength=0;let e,i,n,s=0,a=0,r=0,h=0;this.move=function(t,e){return s=r=t,a=h=e,null},this.line=function(t,e){let i=new Z(r,h,t,e);return r=t,h=e,i},this.curve=function(t,e,i,n,s,a){let o=new $(r,h,t,e,i,n,s,a);return r=s,h=a,o},this.close=function(){let t=new Z(r,h,s,a);return r=s,h=a,t},this.addCommand=function(t){this.pathCommands.push(t);let e=this[t[0]].apply(this,t.slice(3));e&&(e.hasStart=t[1],e.hasEnd=t[2],this.startPoint=this.startPoint||e.startPoint,this.endPoint=e.endPoint,this.pathSegments.push(e),this.totalLength+=e.totalLength)},this.M=function(t,i){return this.addCommand(["move",!0,!0,t,i]),e="M",this},this.m=function(t,e){return this.M(r+t,h+e)},this.Z=this.z=function(){return this.addCommand(["close",!0,!0]),e="Z",this},this.L=function(t,i){return this.addCommand(["line",!0,!0,t,i]),e="L",this},this.l=function(t,e){return this.L(r+t,h+e)},this.H=function(t){return this.L(t,h)},this.h=function(t){return this.L(r+t,h)},this.V=function(t){return this.L(r,t)},this.v=function(t){return this.L(r,h+t)},this.C=function(t,s,a,r,h,o){return this.addCommand(["curve",!0,!0,t,s,a,r,h,o]),e="C",i=a,n=r,this},this.c=function(t,e,i,n,s,a){return this.C(r+t,h+e,r+i,h+n,r+s,h+a)},this.S=function(t,s,a,o){return this.C(r+("C"===e?r-i:0),h+("C"===e?h-n:0),t,s,a,o)},this.s=function(t,s,a,o){return this.C(r+("C"===e?r-i:0),h+("C"===e?h-n:0),r+t,h+s,r+a,h+o)},this.Q=function(t,s,a,o){let l=r+2/3*(t-r),c=h+2/3*(s-h),g=a+2/3*(t-a),u=o+2/3*(s-o);return this.addCommand(["curve",!0,!0,l,c,g,u,a,o]),e="Q",i=t,n=s,this},this.q=function(t,e,i,n){return this.Q(r+t,h+e,r+i,h+n)},this.T=function(t,s){return this.Q(r+("Q"===e?r-i:0),h+("Q"===e?h-n:0),t,s)},this.t=function(t,s){return this.Q(r+("Q"===e?r-i:0),h+("Q"===e?h-n:0),r+t,h+s)},this.A=function(t,i,n,s,a,o,l){if(I(t,0)||I(i,0))this.addCommand(["line",!0,!0,o,l]);else{n*=Math.PI/180,t=Math.abs(t),i=Math.abs(i),s=1*!!s,a=1*!!a;let e=Math.cos(n)*(r-o)/2+Math.sin(n)*(h-l)/2,c=Math.cos(n)*(h-l)/2-Math.sin(n)*(r-o)/2,g=e*e/(t*t)+c*c/(i*i);g>1&&(t*=Math.sqrt(g),i*=Math.sqrt(g));let u=Math.sqrt(Math.max(0,t*t*i*i-t*t*c*c-i*i*e*e)/(t*t*c*c+i*i*e*e)),d=(s===a?-1:1)*u*t*c/i,f=(s===a?1:-1)*u*i*e/t,p=Math.cos(n)*d-Math.sin(n)*f+(r+o)/2,m=Math.sin(n)*d+Math.cos(n)*f+(h+l)/2,b=Math.atan2((c-f)/i,(e-d)/t),w=Math.atan2((-c-f)/i,(-e-d)/t);0===a&&w-b>0?w-=2*Math.PI:1===a&&w-b<0&&(w+=2*Math.PI);let y=Math.ceil(Math.abs(w-b)/(Math.PI/Tt));for(let e=0;e<y;e++){let s=b+e*(w-b)/y,a=b+(e+1)*(w-b)/y,r=4/3*Math.tan((a-s)/4),h=p+Math.cos(n)*t*(Math.cos(s)-r*Math.sin(s))-Math.sin(n)*i*(Math.sin(s)+r*Math.cos(s)),o=m+Math.sin(n)*t*(Math.cos(s)-r*Math.sin(s))+Math.cos(n)*i*(Math.sin(s)+r*Math.cos(s)),l=p+Math.cos(n)*t*(Math.cos(a)+r*Math.sin(a))-Math.sin(n)*i*(Math.sin(a)-r*Math.cos(a)),c=m+Math.sin(n)*t*(Math.cos(a)+r*Math.sin(a))+Math.cos(n)*i*(Math.sin(a)-r*Math.cos(a)),g=p+Math.cos(n)*t*Math.cos(a)-Math.sin(n)*i*Math.sin(a),u=m+Math.sin(n)*t*Math.cos(a)+Math.cos(n)*i*Math.sin(a);this.addCommand(["curve",0===e,e===y-1,h,o,l,c,g,u])}}return e="A",this},this.a=function(t,e,i,n,s,a,o){return this.A(t,e,i,n,s,r+a,h+o)},this.path=function(t){let e,i,n,s=new R((t||"").trim());for(;e=s.match(/^[astvzqmhlcASTVZQMHLC]/);){s.matchSeparator();let t=[];for(;i=l[e+t.length]?s.match(/^[01]/):s.matchNumber();)s.matchSeparator(),t.length===o[e]&&(this[e].apply(this,t),t=[],"M"===e?e="L":"m"===e&&(e="l")),t.push(Number(i));if(t.length!==o[e])return void St("SvgPath: command "+e+" with "+t.length+" numbers");this[e].apply(this,t)}return(n=s.matchAll())&&St("SvgPath: unexpected string "+n),this},this.getBoundingBox=function(){let t=[1/0,1/0,-1/0,-1/0];for(let i=0;i<this.pathSegments.length;i++)(e=this.pathSegments[i].getBoundingBox())[0]<t[0]&&(t[0]=e[0]),e[2]>t[2]&&(t[2]=e[2]),e[1]<t[1]&&(t[1]=e[1]),e[3]>t[3]&&(t[3]=e[3]);var e;return t[0]===1/0&&(t[0]=0),t[1]===1/0&&(t[1]=0),t[2]===-1/0&&(t[2]=0),t[3]===-1/0&&(t[3]=0),t},this.getPointAtLength=function(t){if(t>=0&&t<=this.totalLength){let e;for(let i=0;i<this.pathSegments.length;i++){if(e=this.pathSegments[i].getPointAtLength(t))return e;t-=this.pathSegments[i].totalLength}return this.endPoint}},this.transform=function(t){this.pathSegments=[],this.startPoint=null,this.endPoint=null,this.totalLength=0;for(let e=0;e<this.pathCommands.length;e++){let e=this.pathCommands.shift();for(let i=3;i<e.length;i+=2){let n=P([e[i],e[i+1]],t);e[i]=n[0],e[i+1]=n[1]}this.addCommand(e)}return this},this.mergeShape=function(t){for(let e=0;e<t.pathCommands.length;e++)this.addCommand(t.pathCommands[e].slice());return this},this.clone=function(){return(new X).mergeShape(this)},this.insertInDocument=function(){for(let e=0;e<this.pathCommands.length;e++){let i=this.pathCommands[e][0],n=this.pathCommands[e].slice(3);switch(i){case"move":t.moveTo(n[0],n[1]);break;case"line":t.lineTo(n[0],n[1]);break;case"curve":t.bezierCurveTo(n[0],n[1],n[2],n[3],n[4],n[5]);break;case"close":t.closePath()}}},this.getSubPaths=function(){let t=[],e=new X;for(let i=0;i<this.pathCommands.length;i++){let n=this.pathCommands[i];"move"===this.pathCommands[i][0]&&0!==i&&(t.push(e),e=new X),e.addCommand(n)}return t.push(e),t},this.getMarkers=function(){let t=[],e=this.getSubPaths();for(let i=0;i<e.length;i++){let n=e[i],s=[];for(let t=0;t<n.pathSegments.length;t++){let e=n.pathSegments[t];if(O(e.totalLength,0)||0===t||t===n.pathSegments.length-1){if(e.hasStart){let t=e.getPointAtLength(0),i=s.pop();i&&(t[2]=.5*(i[2]+t[2])),s.push(t)}if(e.hasEnd){let t=e.getPointAtLength(e.totalLength);s.push(t)}}}t=t.concat(s)}return t}},Q=function(t,i){let n=Object.create(null),s=null;this.name=t.nodeName,this.isOuterElement=t===e||!t.parentNode,this.inherits=i||(this.isOuterElement?null:G(t.parentNode,null)),this.stack=this.inherits?this.inherits.stack.concat(t):[t],this.style=W("function"==typeof t.getAttribute&&t.getAttribute("style")),this.css=_t?getComputedStyle(t):function(t){let e=Object.create(null),i=Object.create(null);for(let n=0;n<qt.length;n++){let s=qt[n];if(j(t,s.selector))for(let t in s.css)i[t]>s.selector.specificity||(e[t]=s.css[t],i[t]=s.selector.specificity)}return e}(t),this.allowedChildren=[],this.attr=function(e){if("function"==typeof t.getAttribute)return t.getAttribute(e)},this.resolveUrl=function(t){let i=(t||"").match(/^\s*(?:url\("(.*)#(.*)"\)|url\('(.*)#(.*)'\)|url\((.*)#(.*)\)|(.*)#(.*))\s*$/)||[],n=i[1]||i[3]||i[5]||i[7],s=i[2]||i[4]||i[6]||i[8];if(s){if(!n){let t=e.getElementById(s);if(t)return-1===this.stack.indexOf(t)?t:void St('SVGtoPDF: loop of circular references for id "'+s+'"')}if(Ot){let t=Nt[n];if(!t){t=Ot(n),("object"!=typeof(a=t)||null===a||"number"!=typeof a.length)&&(t=[t]);for(let e=0;e<t.length;e++)"string"==typeof t[e]&&(t[e]=x(t[e]));Nt[n]=t}for(let e=0;e<t.length;e++){let i=t[e].getElementById(s);if(i)return-1===this.stack.indexOf(i)?i:void St('SVGtoPDF: loop of circular references for id "'+n+"#"+s+'"')}}}var a},this.computeUnits=function(t,e,i,n){return"%"===e?parseFloat(t)/100*(n||null!=i?i:this.getViewport()):"ex"===e||"em"===e?t*{em:1,ex:.5}[e]*(n?i:this.get("font-size")):t*{"":1,px:1,pt:96/72,cm:96/2.54,mm:96/25.4,in:96,pc:16}[e]},this.computeLength=function(t,e,i,n){let s,a,r=new R((t||"").trim());return"string"!=typeof(s=r.matchNumber())||"string"!=typeof(a=r.matchLengthUnit())||r.matchAll()?i:this.computeUnits(s,a,e,n)},this.computeLengthList=function(t,e,i){let n,s,a=new R((t||"").trim()),r=[];for(;"string"==typeof(n=a.matchNumber())&&"string"==typeof(s=a.matchLengthUnit());)r.push(this.computeUnits(n,s,e)),a.matchSeparator();if(!i||!a.matchAll())return r},this.getLength=function(t,e,i){return this.computeLength(this.attr(t),e,i)},this.getLengthList=function(t,e){return this.computeLengthList(this.attr(t),e)},this.getUrl=function(t){return this.resolveUrl(this.attr(t))},this.getNumberList=function(t){let e,i=new R((this.attr(t)||"").trim()),n=[];for(;e=i.matchNumber();)n.push(Number(e)),i.matchSeparator();return n.error=i.matchAll(),n},this.getViewbox=function(t,e){let i=this.getNumberList(t);return 4===i.length&&i[2]>=0&&i[3]>=0?i:e},this.getPercent=function(t,e){let i=this.attr(t),n=new R((i||"").trim()),s=n.matchNumber();return s?(n.match("%")&&(s*=.01),n.matchAll()?e:Math.max(0,Math.min(1,s))):e},this.chooseValue=function(t){for(let t=0;t<arguments.length;t++)if(null!=arguments[t]&&arguments[t]==arguments[t])return arguments[t];return arguments[arguments.length-1]},this.get=function(t){if(void 0!==n[t])return n[t];let e,i,s=c[t]||{};for(let a=0;a<3;a++){switch(a){case 0:"transform"!==t&&(e=this.css[s.css||t]);break;case 1:e=this.style[t];break;case 2:e=this.attr(t)}if("inherit"===e&&null!=(i=this.inherits?this.inherits.get(t):s.initial))return n[t]=i;if(null!=s.values&&null!=(i=s.values[e]))return n[t]=i;if(null!=e){let a;switch(t){case"font-size":i=this.computeLength(e,this.inherits?this.inherits.get(t):s.initial,void 0,!0);break;case"baseline-shift":i=this.computeLength(e,this.get("font-size"));break;case"font-family":i=e||void 0;break;case"opacity":case"stroke-opacity":case"fill-opacity":case"stop-opacity":a=parseFloat(e),isNaN(a)||(i=Math.max(0,Math.min(1,a)));break;case"transform":i=H(e);break;case"stroke-dasharray":if("none"===e)i=[];else if(a=this.computeLengthList(e,this.getViewport(),!0)){let t=0,e=!1;for(let i=0;i<a.length;i++)a[i]<0&&(e=!0),t+=a[i];e||(a.length%2==1&&(a=a.concat(a)),i=0===t?[]:a)}break;case"color":i="none"===e||"transparent"===e?"none":M(e);break;case"fill":case"stroke":if("none"===e||"transparent"===e)i="none";else if("currentColor"===e)i=this.get("color");else{if(a=M(e))return a;if(a=(e||"").split(" ")){let t=this.resolveUrl(a[0]),e=M(a[1]);i=null==t?e:"linearGradient"===t.nodeName||"radialGradient"===t.nodeName?new ht(t,null,e):"pattern"===t.nodeName?new rt(t,null,e):e}}break;case"stop-color":i="none"===e||"transparent"===e?"none":"currentColor"===e?this.get("color"):M(e);break;case"marker-start":case"marker-mid":case"marker-end":case"clip-path":case"mask":i="none"===e?"none":this.resolveUrl(e);break;case"stroke-width":null!=(a=this.computeLength(e,this.getViewport()))&&a>=0&&(i=a);break;case"stroke-miterlimit":null!=(a=parseFloat(e))&&a>=1&&(i=a);break;case"word-spacing":case"letter-spacing":i=this.computeLength(e,this.getViewport());break;case"stroke-dashoffset":if(null!=(i=this.computeLength(e,this.getViewport()))&&i<0){let t=this.get("stroke-dasharray");for(let e=0;e<t.length;e++)i+=t[e]}}if(null!=i)return n[t]=i}}return n[t]=s.inherit&&this.inherits?this.inherits.get(t):s.initial},this.getChildren=function(){if(null!=s)return s;let e=[];for(let i=0;i<t.childNodes.length;i++){let n=t.childNodes[i];n.error||-1===this.allowedChildren.indexOf(n.nodeName)||e.push(G(n,this))}return s=e},this.getParentVWidth=function(){return this.inherits?this.inherits.getVWidth():Pt},this.getParentVHeight=function(){return this.inherits?this.inherits.getVHeight():Vt},this.getParentViewport=function(){return Math.sqrt(.5*this.getParentVWidth()*this.getParentVWidth()+.5*this.getParentVHeight()*this.getParentVHeight())},this.getVWidth=function(){return this.getParentVWidth()},this.getVHeight=function(){return this.getParentVHeight()},this.getViewport=function(){return Math.sqrt(.5*this.getVWidth()*this.getVWidth()+.5*this.getVHeight()*this.getVHeight())},this.getBoundingBox=function(){return this.getBoundingShape().getBoundingBox()}},Y=function(e,i){Q.call(this,e,i),this.transform=function(){t.transform.apply(t,this.getTransformation())},this.clip=function(){if("none"!==this.get("clip-path")){return new bt(this.get("clip-path"),null).useMask(this.getBoundingBox()),!0}},this.mask=function(){if("none"!==this.get("mask")){return new wt(this.get("mask"),null).useMask(this.getBoundingBox()),!0}},this.getFill=function(t,e){let i=this.get("opacity"),n=this.get("fill"),s=this.get("fill-opacity");return t?r.white:"none"!==n&&i&&s?n instanceof ht||n instanceof rt?n.getPaint(this.getBoundingBox(),s*i,t,e):v(n,s*i,e):void 0},this.getStroke=function(t,e){let i=this.get("opacity"),n=this.get("stroke"),s=this.get("stroke-opacity");if(!t&&!I(this.get("stroke-width"),0))return"none"!==n&&i&&s?n instanceof ht||n instanceof rt?n.getPaint(this.getBoundingBox(),s*i,t,e):v(n,s*i,e):void 0}},K=function(t,e){Y.call(this,t,e),this.allowedChildren=["use","g","a","svg","image","rect","circle","ellipse","line","polyline","polygon","path","text"],this.getBoundingShape=function(){let t=new X,e=this.getChildren();for(let i=0;i<e.length;i++)if("none"!==e[i].get("display")&&"function"==typeof e[i].getBoundingShape){let n=e[i].getBoundingShape().clone();"function"==typeof e[i].getTransformation&&n.transform(e[i].getTransformation()),t.mergeShape(n)}return t},this.drawChildren=function(t,e){let i=this.getChildren();for(let n=0;n<i.length;n++)"none"!==i[n].get("display")&&"function"==typeof i[n].drawInDocument&&i[n].drawInDocument(t,e)}},J=function(e,i){K.call(this,e,i),this.drawContent=function(e,i){this.transform();let n,s=this.clip(),a=this.mask();(this.get("opacity")<1||s||a)&&!e&&(n=g(C())),this.drawChildren(e,i),n&&(u(n),t.fillOpacity(this.get("opacity")),d(n))}},tt=function(e,i){J.call(this,e,i);let n=this.getLength("x",this.getVWidth(),0),s=this.getLength("y",this.getVHeight(),0),a=this.getUrl("href")||this.getUrl("xlink:href");a&&(a=G(a,this)),this.getChildren=function(){return a?[a]:[]},this.drawInDocument=function(e,i){t.save(),this.drawContent(e,i),t.restore()},this.getTransformation=function(){return L(this.get("transform"),[1,0,0,1,n,s])}},et=function(e,i){J.call(this,e,i);let n=this.getLength("width",this.getParentVWidth(),this.getParentVWidth()),s=this.getLength("height",this.getParentVHeight(),this.getParentVHeight());i instanceof tt&&(n=i.getLength("width",i.getParentVWidth(),n),s=i.getLength("height",i.getParentVHeight(),s));let a=(this.attr("preserveAspectRatio")||"").trim(),r=this.getViewbox("viewBox",[0,0,n,s]);this.getVWidth=function(){return r[2]},this.getVHeight=function(){return r[3]},this.drawInDocument=function(e,i){t.save(),this.drawContent(e,i),t.restore()},this.getTransformation=function(){return L(N(a,n,s,r[2],r[3]),[1,0,0,1,-r[0],-r[1]])}},it=function(e,i){J.call(this,e,i),this.drawInDocument=function(e,i){t.save(),!this.link||e||i||this.addLink(),this.drawContent(e,i),t.restore()},this.getTransformation=function(){return this.get("transform")}},nt=function(e,i){i&&i.isText?(kt.call(this,e,i),this.allowedChildren=["textPath","tspan","#text","#cdata-section","a"]):it.call(this,e,i),this.link=this.attr("href")||this.attr("xlink:href"),this.addLink=function(){if(this.link.match(/^(?:[a-z][a-z0-9+.-]*:|\/\/)?/i)&&this.getChildren().length){let e=this.getBoundingShape().transform(V()).getBoundingBox();!function(e,i,n,s,a){let r=t.ref({Type:"Annot",Subtype:"Link",Rect:[e,i,n,s],Border:[0,0,0],A:{S:"URI",URI:new String(a)}});r.end(),Wt.push(r)}(e[0],e[1],e[2],e[3],this.link)}}},st=function(e,i){J.call(this,e,i);let n=this.getLength("width",this.getParentVWidth(),this.getParentVWidth()),s=this.getLength("height",this.getParentVHeight(),this.getParentVHeight()),a=this.getLength("x",this.getParentVWidth(),0),r=this.getLength("y",this.getParentVHeight(),0);i instanceof tt&&(n=i.getLength("width",i.getParentVWidth(),n),s=i.getLength("height",i.getParentVHeight(),s));let h=this.attr("preserveAspectRatio"),o=this.getViewbox("viewBox",[0,0,n,s]);this.isOuterElement&&Ct&&(a=r=0,n=Pt,s=Vt,h=Ct),this.getVWidth=function(){return o[2]},this.getVHeight=function(){return o[3]},this.drawInDocument=function(e,i){t.save(),"hidden"===this.get("overflow")&&((new X).M(a,r).L(a+n,r).L(a+n,r+s).L(a,r+s).Z().transform(this.get("transform")).insertInDocument(),t.clip()),this.drawContent(e,i),t.restore()},this.getTransformation=function(){return L(this.get("transform"),[1,0,0,1,a,r],N(h,n,s,o[2],o[3]),[1,0,0,1,-o[0],-o[1]])}},at=function(e,i){Y.call(this,e,i);let n,s=At(this.attr("href")||this.attr("xlink:href")||""),a=this.getLength("x",this.getVWidth(),0),h=this.getLength("y",this.getVHeight(),0),o=this.getLength("width",this.getVWidth(),"auto"),l=this.getLength("height",this.getVHeight(),"auto");try{n=t.openImage(s)}catch(t){St('SVGElemImage: failed to open image "'+s+'" in PDFKit')}n&&("auto"===o&&"auto"!==l?o=l*n.width/n.height:"auto"===l&&"auto"!==o?l=o*n.height/n.width:"auto"===o&&"auto"===l&&(o=n.width,l=n.height)),("auto"===o||o<0)&&(o=0),("auto"===l||l<0)&&(l=0),this.getTransformation=function(){return this.get("transform")},this.getBoundingShape=function(){return(new X).M(a,h).L(a+o,h).M(a+o,h+l).L(a,h+l)},this.drawInDocument=function(e,i){"hidden"!==this.get("visibility")&&n&&(t.save(),this.transform(),"hidden"===this.get("overflow")&&t.rect(a,h,o,l).clip(),this.clip(),this.mask(),t.translate(a,h),t.transform.apply(t,N(this.attr("preserveAspectRatio"),o,l,n?n.width:o,n?n.height:l)),e?(t.rect(0,0,n.width,n.height),w(r.white).fill()):(t.fillOpacity(this.get("opacity")),t.image(n,0,0)),t.restore())}},rt=function(e,i,n){K.call(this,e,i),this.ref=function(){let t=this.getUrl("href")||this.getUrl("xlink:href");if(t&&t.nodeName===e.nodeName)return new rt(t,i,n)}.call(this);let s=this.attr;this.attr=function(t){let e=s.call(this,t);return null!=e||"href"===t||"xlink:href"===t?e:this.ref?this.ref.attr(t):null};let a=this.getChildren;this.getChildren=function(){let t=a.call(this);return t.length>0?t:this.ref?this.ref.getChildren():[]},this.getPaint=function(e,i,s,a){let r="userSpaceOnUse"!==this.attr("patternUnits"),h="objectBoundingBox"===this.attr("patternContentUnits"),o=this.getLength("x",r?1:this.getParentVWidth(),0),l=this.getLength("y",r?1:this.getParentVHeight(),0),c=this.getLength("width",r?1:this.getParentVWidth(),0),d=this.getLength("height",r?1:this.getParentVHeight(),0);h&&!r?(o=(o-e[0])/(e[2]-e[0])||0,l=(l-e[1])/(e[3]-e[1])||0,c=c/(e[2]-e[0])||0,d=d/(e[3]-e[1])||0):!h&&r&&(o=e[0]+o*(e[2]-e[0]),l=e[1]+l*(e[3]-e[1]),c*=e[2]-e[0],d*=e[3]-e[1]);let f=this.getViewbox("viewBox",[0,0,c,d]),m=L(N((this.attr("preserveAspectRatio")||"").trim(),c,d,f[2],f[3],0),[1,0,0,1,-f[0],-f[1]]),b=H(this.attr("patternTransform"));if(h&&(b=L([e[2]-e[0],0,0,e[3]-e[1],e[0],e[1]],b)),(b=S(b=L(b,[1,0,0,1,o,l])))&&(m=S(m))&&(c=T(c))&&(d=T(d))){let e=g([0,0,c,d]);return t.transform.apply(t,m),this.drawChildren(s,a),u(e),[p(e,c,d,b),i]}return n?[n[0],n[1]*i]:void 0},this.getVWidth=function(){let t="userSpaceOnUse"!==this.attr("patternUnits"),e=this.getLength("width",t?1:this.getParentVWidth(),0);return this.getViewbox("viewBox",[0,0,e,0])[2]},this.getVHeight=function(){let t="userSpaceOnUse"!==this.attr("patternUnits"),e=this.getLength("height",t?1:this.getParentVHeight(),0);return this.getViewbox("viewBox",[0,0,0,e])[3]}},ht=function(e,i,n){Q.call(this,e,i),this.allowedChildren=["stop"],this.ref=function(){let t=this.getUrl("href")||this.getUrl("xlink:href");if(t&&t.nodeName===e.nodeName)return new ht(t,i,n)}.call(this);let s=this.attr;this.attr=function(t){let e=s.call(this,t);return null!=e||"href"===t||"xlink:href"===t?e:this.ref?this.ref.attr(t):null};let a=this.getChildren;this.getChildren=function(){let t=a.call(this);return t.length>0?t:this.ref?this.ref.getChildren():[]},this.getPaint=function(e,i,s,a){let h=this.getChildren();if(0===h.length)return;if(1===h.length){let t=h[0],e=t.get("stop-color");if("none"===e)return;return v(e,t.get("stop-opacity")*i,a)}let o,l,c,g,u,d,f="userSpaceOnUse"!==this.attr("gradientUnits"),p=H(this.attr("gradientTransform")),m=this.attr("spreadMethod"),b=0,w=0,y=1;if(f&&(p=L([e[2]-e[0],0,0,e[3]-e[1],e[0],e[1]],p)),p=S(p)){if("linearGradient"===this.name)l=this.getLength("x1",f?1:this.getVWidth(),0),c=this.getLength("x2",f?1:this.getVWidth(),f?1:this.getVWidth()),g=this.getLength("y1",f?1:this.getVHeight(),0),u=this.getLength("y2",f?1:this.getVHeight(),0);else{c=this.getLength("cx",f?1:this.getVWidth(),f?.5:.5*this.getVWidth()),u=this.getLength("cy",f?1:this.getVHeight(),f?.5:.5*this.getVHeight()),d=this.getLength("r",f?1:this.getViewport(),f?.5:.5*this.getViewport()),l=this.getLength("fx",f?1:this.getVWidth(),c),g=this.getLength("fy",f?1:this.getVHeight(),u),d<0&&St("SvgElemGradient: negative r value");let t=Math.sqrt(Math.pow(c-l,2)+Math.pow(u-g,2)),e=1;t>d&&(l=c+(l-c)*(e=d/t),g=u+(g-u)*e),d=Math.max(d,t*e*(1+1e-6))}if("reflect"===m||"repeat"===m){let t=_(p),i=P([e[0],e[1]],t),n=P([e[2],e[1]],t),s=P([e[2],e[3]],t),a=P([e[0],e[3]],t);"linearGradient"===this.name?(b=Math.max((i[0]-c)*(c-l)+(i[1]-u)*(u-g),(n[0]-c)*(c-l)+(n[1]-u)*(u-g),(s[0]-c)*(c-l)+(s[1]-u)*(u-g),(a[0]-c)*(c-l)+(a[1]-u)*(u-g))/(Math.pow(c-l,2)+Math.pow(u-g,2)),w=Math.max((i[0]-l)*(l-c)+(i[1]-g)*(g-u),(n[0]-l)*(l-c)+(n[1]-g)*(g-u),(s[0]-l)*(l-c)+(s[1]-g)*(g-u),(a[0]-l)*(l-c)+(a[1]-g)*(g-u))/(Math.pow(c-l,2)+Math.pow(u-g,2))):b=Math.sqrt(Math.max(Math.pow(i[0]-c,2)+Math.pow(i[1]-u,2),Math.pow(n[0]-c,2)+Math.pow(n[1]-u,2),Math.pow(s[0]-c,2)+Math.pow(s[1]-u,2),Math.pow(a[0]-c,2)+Math.pow(a[1]-u,2)))/d-1,b=Math.ceil(b+.5),y=(w=Math.ceil(w+.5))+1+b}o="linearGradient"===this.name?t.linearGradient(l-w*(c-l),g-w*(u-g),c+b*(c-l),u+b*(u-g)):t.radialGradient(l,g,0,c,u,d+b*d);for(let t=0;t<y;t++){let e=0,n="reflect"!==m||(t-w)%2==0;for(let s=0;s<h.length;s++){let l=h[n?s:h.length-1-s],c=l.get("stop-color");"none"===c&&(c=r.transparent),c=v(c,l.get("stop-opacity")*i,a),e=Math.max(e,n?l.getPercent("offset",0):1-l.getPercent("offset",0)),0===s&&4===c[0].length&&(o._colorSpace="DeviceCMYK"),0===s&&e>0&&o.stop((t+0)/y,c[0],c[1]),o.stop((t+e)/(b+w+1),c[0],c[1]),s===h.length-1&&e<1&&o.stop((t+1)/y,c[0],c[1])}}return o.setTransform.apply(o,p),[o,1]}return n?[n[0],n[1]*i]:void 0}},ot=function(e,i){Y.call(this,e,i),this.dashScale=1,this.getBoundingShape=function(){return this.shape},this.getTransformation=function(){return this.get("transform")},this.drawInDocument=function(e,i){if("hidden"!==this.get("visibility")&&this.shape){if(t.save(),this.transform(),this.clip(),e)this.shape.insertInDocument(),w(r.white),t.fill(this.get("clip-rule"));else{let n;this.mask()&&(n=g(C()));let s=this.shape.getSubPaths(),a=this.getFill(e,i),r=this.getStroke(e,i),h=this.get("stroke-width"),o=this.get("stroke-linecap");if(a||r){if(a&&w(a),r){for(let e=0;e<s.length;e++)if(I(s[e].totalLength,0)&&("square"===o||"round"===o)&&h>0){let i=s[e].startPoint[0],n=s[e].startPoint[1];w(r),"square"===o?t.rect(i-.5*h,n-.5*h,h,h):"round"===o&&t.circle(i,n,.5*h),t.fill()}let e=this.get("stroke-dasharray"),i=this.get("stroke-dashoffset");if(O(this.dashScale,1)){for(let t=0;t<e.length;t++)e[t]*=this.dashScale;i*=this.dashScale}y(r),t.lineWidth(h).miterLimit(this.get("stroke-miterlimit")).lineJoin(this.get("stroke-linejoin")).lineCap(o).dash(e,{phase:i})}for(let t=0;t<s.length;t++)s[t].totalLength>0&&s[t].insertInDocument();a&&r?t.fillAndStroke(this.get("fill-rule")):a?t.fill(this.get("fill-rule")):r&&t.stroke()}let l=this.get("marker-start"),c=this.get("marker-mid"),f=this.get("marker-end");if("none"!==l||"none"!==c||"none"!==f){let t=this.shape.getMarkers();if("none"!==l){new mt(l,null).drawMarker(!1,i,t[0],h)}if("none"!==c)for(let e=1;e<t.length-1;e++){new mt(c,null).drawMarker(!1,i,t[e],h)}if("none"!==f){new mt(f,null).drawMarker(!1,i,t[t.length-1],h)}}n&&(u(n),d(n))}t.restore()}}},lt=function(t,e){ot.call(this,t,e);let i=this.getLength("x",this.getVWidth(),0),n=this.getLength("y",this.getVHeight(),0),s=this.getLength("width",this.getVWidth(),0),a=this.getLength("height",this.getVHeight(),0),r=this.getLength("rx",this.getVWidth()),h=this.getLength("ry",this.getVHeight());void 0===r&&void 0===h?r=h=0:void 0===r&&void 0!==h?r=h:void 0!==r&&void 0===h&&(h=r),s>0&&a>0?r&&h?(r=Math.min(r,.5*s),h=Math.min(h,.5*a),this.shape=(new X).M(i+r,n).L(i+s-r,n).A(r,h,0,0,1,i+s,n+h).L(i+s,n+a-h).A(r,h,0,0,1,i+s-r,n+a).L(i+r,n+a).A(r,h,0,0,1,i,n+a-h).L(i,n+h).A(r,h,0,0,1,i+r,n).Z()):this.shape=(new X).M(i,n).L(i+s,n).L(i+s,n+a).L(i,n+a).Z():this.shape=new X},ct=function(t,e){ot.call(this,t,e);let i=this.getLength("cx",this.getVWidth(),0),n=this.getLength("cy",this.getVHeight(),0),s=this.getLength("r",this.getViewport(),0);this.shape=s>0?(new X).M(i+s,n).A(s,s,0,0,1,i-s,n).A(s,s,0,0,1,i+s,n).Z():new X},gt=function(t,e){ot.call(this,t,e);let i=this.getLength("cx",this.getVWidth(),0),n=this.getLength("cy",this.getVHeight(),0),s=this.getLength("rx",this.getVWidth(),0),a=this.getLength("ry",this.getVHeight(),0);this.shape=s>0&&a>0?(new X).M(i+s,n).A(s,a,0,0,1,i-s,n).A(s,a,0,0,1,i+s,n).Z():new X},ut=function(t,e){ot.call(this,t,e);let i=this.getLength("x1",this.getVWidth(),0),n=this.getLength("y1",this.getVHeight(),0),s=this.getLength("x2",this.getVWidth(),0),a=this.getLength("y2",this.getVHeight(),0);this.shape=(new X).M(i,n).L(s,a)},dt=function(t,e){ot.call(this,t,e);let i=this.getNumberList("points");this.shape=new X;for(let t=0;t<i.length-1;t+=2)0===t?this.shape.M(i[t],i[t+1]):this.shape.L(i[t],i[t+1]);i.error&&St("SvgElemPolygon: unexpected string "+i.error),i.length%2==1&&St("SvgElemPolyline: uneven number of coordinates")},ft=function(t,e){ot.call(this,t,e);let i=this.getNumberList("points");this.shape=new X;for(let t=0;t<i.length-1;t+=2)0===t?this.shape.M(i[t],i[t+1]):this.shape.L(i[t],i[t+1]);this.shape.Z(),i.error&&St("SvgElemPolygon: unexpected string "+i.error),i.length%2==1&&St("SvgElemPolygon: uneven number of coordinates")},pt=function(t,e){ot.call(this,t,e),this.shape=(new X).path(this.attr("d"));let i=this.getLength("pathLength",this.getViewport());this.pathLength=i>0?i:void 0,this.dashScale=void 0!==this.pathLength?this.shape.totalLength/this.pathLength:1},mt=function(e,i){K.call(this,e,i);let n=this.getLength("markerWidth",this.getParentVWidth(),3),s=this.getLength("markerHeight",this.getParentVHeight(),3),a=this.getViewbox("viewBox",[0,0,n,s]);this.getVWidth=function(){return a[2]},this.getVHeight=function(){return a[3]},this.drawMarker=function(e,i,r,h){t.save();let o=this.attr("orient"),l=this.attr("markerUnits"),c="auto"===o?r[2]:(parseFloat(o)||0)*Math.PI/180,f="userSpaceOnUse"===l?1:h;t.transform(Math.cos(c)*f,Math.sin(c)*f,-Math.sin(c)*f,Math.cos(c)*f,r[0],r[1]);let p,m=this.getLength("refX",this.getVWidth(),0),b=this.getLength("refY",this.getVHeight(),0),w=N(this.attr("preserveAspectRatio"),n,s,a[2],a[3],.5);"hidden"===this.get("overflow")&&t.rect(w[0]*(a[0]+a[2]/2-m)-n/2,w[3]*(a[1]+a[3]/2-b)-s/2,n,s).clip(),t.transform.apply(t,w),t.translate(-m,-b),this.get("opacity")<1&&!e&&(p=g(C())),this.drawChildren(e,i),p&&(u(p),t.fillOpacity(this.get("opacity")),d(p)),t.restore()}},bt=function(e,i){K.call(this,e,i),this.useMask=function(e){let i=g(C());t.save(),"objectBoundingBox"===this.attr("clipPathUnits")&&t.transform(e[2]-e[0],0,0,e[3]-e[1],e[0],e[1]),this.clip(),this.drawChildren(!0,!1),t.restore(),u(i),f(i,!0)}},wt=function(e,i){K.call(this,e,i),this.useMask=function(e){let i,n,s,a,r=g(C());t.save(),"userSpaceOnUse"===this.attr("maskUnits")?(i=this.getLength("x",this.getVWidth(),-.1*(e[2]-e[0])+e[0]),n=this.getLength("y",this.getVHeight(),-.1*(e[3]-e[1])+e[1]),s=this.getLength("width",this.getVWidth(),1.2*(e[2]-e[0])),a=this.getLength("height",this.getVHeight(),1.2*(e[3]-e[1]))):(i=this.getLength("x",this.getVWidth(),-.1)*(e[2]-e[0])+e[0],n=this.getLength("y",this.getVHeight(),-.1)*(e[3]-e[1])+e[1],s=this.getLength("width",this.getVWidth(),1.2)*(e[2]-e[0]),a=this.getLength("height",this.getVHeight(),1.2)*(e[3]-e[1])),t.rect(i,n,s,a).clip(),"objectBoundingBox"===this.attr("maskContentUnits")&&t.transform(e[2]-e[0],0,0,e[3]-e[1],e[0],e[1]),this.clip(),this.drawChildren(!1,!0),t.restore(),u(r),f(r,!0)}},yt=function(e,i){Y.call(this,e,i),this.allowedChildren=["tspan","#text","#cdata-section","a"],this.isText=!0,this.getBoundingShape=function(){let t=new X;for(let e=0;e<this._pos.length;e++){let i=this._pos[e];if(!i.hidden){let e=i.ascent*Math.sin(i.rotate),n=-i.ascent*Math.cos(i.rotate),s=i.descent*Math.sin(i.rotate),a=-i.descent*Math.cos(i.rotate),r=i.width*Math.cos(i.rotate),h=i.width*Math.sin(i.rotate);t.M(i.x+e,i.y+n).L(i.x+e+r,i.y+n+h).M(i.x+s+r,i.y+a+h).L(i.x+s,i.y+a)}}return t},this.drawTextInDocument=function(e,i){!this.link||e||i||this.addLink(),"underline"===this.get("text-decoration")&&this.decorate(.05*this._font.size,-.075*this._font.size,e,i),"overline"===this.get("text-decoration")&&this.decorate(.05*this._font.size,D(this._font.font,this._font.size)+.075*this._font.size,e,i);let n=this.getFill(e,i),s=this.getStroke(e,i),a=this.get("stroke-width");this._font.fauxBold&&(s?a+=.03*this._font.size:(s=n,a=.03*this._font.size));let r=this.getChildren();for(let m=0;m<r.length;m++){let x=r[m];switch(x.name){case"tspan":case"textPath":case"a":"none"!==x.get("display")&&x.drawTextInDocument(e,i);break;case"#text":case"#cdata-section":if("hidden"===this.get("visibility"))continue;if(n||s||e){n&&w(n),s&&a&&(y(s),t.lineWidth(a).miterLimit(this.get("stroke-miterlimit")).lineJoin(this.get("stroke-linejoin")).lineCap(this.get("stroke-linecap")).dash(this.get("stroke-dasharray"),{phase:this.get("stroke-dashoffset")})),f=this._font.font,p=this._font.size,t.page.fonts[f.id]||(t.page.fonts[f.id]=f.ref()),t.addContent("BT").addContent("/"+f.id+" "+p+" Tf"),b(!!n,!!s);for(let e=0,i=x._pos;e<i.length;e++)if(!i[e].hidden&&O(i[e].width,0)){let n=Math.cos(i[e].rotate),s=Math.sin(i[e].rotate),a=this._font.fauxItalic?-.25:0;o=n*i[e].scale,l=s*i[e].scale,c=n*a-s,g=s*a+n,u=i[e].x,d=i[e].y,t.addContent(T(o)+" "+T(l)+" "+T(-c)+" "+T(-g)+" "+T(u)+" "+T(d)+" Tm"),h=i[e].glyph,t.addContent("<"+h+"> Tj")}t.addContent("ET")}}}var h,o,l,c,g,u,d,f,p;"line-through"===this.get("text-decoration")&&this.decorate(.05*this._font.size,.5*(D(this._font.font,this._font.size)+F(this._font.font,this._font.size)),e,i)},this.decorate=function(e,i,n,s){let a=this.getFill(n,s),r=this.getStroke(n,s);a&&w(a),r&&(y(r),t.lineWidth(this.get("stroke-width")).miterLimit(this.get("stroke-miterlimit")).lineJoin(this.get("stroke-linejoin")).lineCap(this.get("stroke-linecap")).dash(this.get("stroke-dasharray"),{phase:this.get("stroke-dashoffset")}));for(let n=0,s=this._pos;n<s.length;n++)if(!s[n].hidden&&O(s[n].width,0)){let h=(i+e/2)*Math.sin(s[n].rotate),o=-(i+e/2)*Math.cos(s[n].rotate),l=(i-e/2)*Math.sin(s[n].rotate),c=-(i-e/2)*Math.cos(s[n].rotate),g=s[n].width*Math.cos(s[n].rotate),u=s[n].width*Math.sin(s[n].rotate);(new X).M(s[n].x+h,s[n].y+o).L(s[n].x+h+g,s[n].y+o+u).L(s[n].x+l+g,s[n].y+c+u).L(s[n].x+l,s[n].y+c).Z().insertInDocument(),a&&r?t.fillAndStroke():a?t.fill():r&&t.stroke()}}},xt=function(t,e){this.name=t.nodeName,this.textContent=t.nodeValue},kt=function(t,e){yt.call(this,t,e)},Mt=function(t,e){let i;if(yt.call(this,t,e),(i=this.attr("path"))&&""!==i.trim()){let t=this.getLength("pathLength",this.getViewport());this.pathObject=(new X).path(i),this.pathLength=t>0?t:this.pathObject.totalLength,this.pathScale=this.pathObject.totalLength/this.pathLength}else if((i=this.getUrl("href")||this.getUrl("xlink:href"))&&"path"===i.nodeName){let t=new pt(i,this);this.pathObject=t.shape.clone().transform(t.get("transform")),this.pathLength=this.chooseValue(t.pathLength,this.pathObject.totalLength),this.pathScale=this.pathObject.totalLength/this.pathLength}},vt=function(e,i){yt.call(this,e,i),this.allowedChildren=["textPath","tspan","#text","#cdata-section","a"],function(i){let n,s,a="",r=e.textContent,h=[],o=[],l=0,c=0;function g(){if(o.length){let t=o[o.length-1],e=o[0],i=t.x+t.width-e.x,a={startltr:0,middleltr:.5,endltr:1,startrtl:1,middlertl:.5,endrtl:0}[n+s]*i||0;for(let t=0;t<o.length;t++)o[t].x-=a}o=[]}function u(t){let e=t.pathObject,i=t.pathLength,n=t.pathScale;if(e){let s=t.getLength("startOffset",i,0);for(let a=0;a<t._pos.length;a++){let r=s+t._pos[a].x+.5*t._pos[a].width;if(r>i||r<0)t._pos[a].hidden=!0;else{let i=e.getPointAtLength(r*n);O(n,1)&&(t._pos[a].scale*=n,t._pos[a].width*=n),t._pos[a].x=i[0]-.5*t._pos[a].width*Math.cos(i[2])-t._pos[a].y*Math.sin(i[2]),t._pos[a].y=i[1]-.5*t._pos[a].width*Math.sin(i[2])+t._pos[a].y*Math.cos(i[2]),t._pos[a].rotate=i[2]+t._pos[a].rotate,t._pos[a].continuous=!1}}}else for(let e=0;e<t._pos.length;e++)t._pos[e].hidden=!0}!function e(i,u){i._x=U(i.getLengthList("x",i.getVWidth()),u?u._x.slice(u._pos.length):[]),i._y=U(i.getLengthList("y",i.getVHeight()),u?u._y.slice(u._pos.length):[]),i._dx=U(i.getLengthList("dx",i.getVWidth()),u?u._dx.slice(u._pos.length):[]),i._dy=U(i.getLengthList("dy",i.getVHeight()),u?u._dy.slice(u._pos.length):[]),i._rot=U(i.getNumberList("rotate"),u?u._rot.slice(u._pos.length):[]),i._defRot=i.chooseValue(i._rot[i._rot.length-1],u&&u._defRot,0),"textPath"===i.name&&(i._y=[]);let d={fauxItalic:!1,fauxBold:!1},f=Bt(i.get("font-family"),"bold"===i.get("font-weight"),"italic"===i.get("font-style"),d);try{t.font(f)}catch(t){St('SVGElemText: failed to open font "'+f+'" in PDFKit')}i._pos=[],i._index=0,i._font={font:t._font,size:i.get("font-size"),fauxItalic:d.fauxItalic,fauxBold:d.fauxBold};let p=i.getLength("textLength",i.getVWidth(),void 0),m="spacingAndGlyphs"===i.attr("lengthAdjust"),b=i.get("word-spacing"),w=i.get("letter-spacing"),y=i.get("text-anchor"),x=i.get("direction"),k=function(t,e,i,n){let s,a;switch(i){case"middle":s=.5*function(t,e){return(t.xHeight||.5*(t.ascender-t.descender))*e/1e3}(t,e);break;case"central":s=.5*(F(t,e)+D(t,e));break;case"after-edge":case"text-after-edge":s=F(t,e);break;case"alphabetic":case"auto":case"baseline":s=0;break;case"mathematical":s=.5*D(t,e);break;case"hanging":s=.8*D(t,e);break;case"before-edge":case"text-before-edge":s=D(t,e);break;default:s=0}switch(n){case"baseline":a=0;break;case"super":a=.6*e;break;case"sub":a=-.6*e;break;default:a=n}return s-a}(i._font.font,i._font.size,i.get("alignment-baseline")||i.get("dominant-baseline"),i.get("baseline-shift"));"textPath"===i.name&&(g(),l=c=0);let M=i.getChildren();for(let t=0;t<M.length;t++){let h=M[t];switch(h.name){case"tspan":case"textPath":case"a":e(h,i);break;case"#text":case"#cdata-section":let t,u=h.textContent,d=u;h._font=i._font,h._pos=[],r=r.substring(u.length),"preserve"===i.get("xml:space")?d=d.replace(/[\s]/g," "):(d=d.replace(/[\s]+/g," "),a.match(/[\s]$|^$/)&&(d=d.replace(/^[\s]/,"")),r.match(/^[\s]*$/)&&(d=d.replace(/[\s]$/,""))),a+=u,t=0===b?[d]:d.split(/(\s)/);for(let e=0;e<t.length;e++){let a=E(i._font.font,i._font.size,t[e]);for(let t=0;t<a.length;t++){let r=i._index,u=i._x[r],d=i._y[r],f=i._dx[r],p=i._dy[r],m=i._rot[r],b=!(0===e&&0===t);void 0!==u&&(b=!1,g(),l=u),void 0!==d&&(b=!1,g(),c=d),void 0!==f&&(b=!1,l+=f),void 0!==p&&(b=!1,c+=p),void 0===m&&0===i._defRot||(b=!1);let M={glyph:a[t].glyph,rotate:Math.PI/180*i.chooseValue(m,i._defRot),x:l+a[t].xOffset,y:c+k+a[t].yOffset,width:a[t].width,ascent:D(i._font.font,i._font.size),descent:F(i._font.font,i._font.size),scale:1,hidden:!1,continuous:b};o.push(M),h._pos.push(M),i._pos.push(M),i._index+=a[t].unicode.length,1===o.length&&(n=y,s=x),l+=a[t].xAdvance+w,c+=a[t].yAdvance}" "===t[e]&&(l+=b)}break;default:r=r.substring(h.textContent.length)}}if(p&&i._pos.length&&function(t,e,i){let n=t[0],s=t[t.length-1],a=n.x,r=s.x+s.width;if(i){let i=e/(r-a);if(i>0&&i<1/0)for(let e=0;e<t.length;e++)t[e].x=a+i*(t[e].x-a),t[e].scale*=i,t[e].width*=i}else if(t.length>=2){let i=(e-(r-a))/(t.length-1);for(let e=0;e<t.length;e++)t[e].x+=e*i}l+=e-(r-a)}(i._pos,p,m),"textPath"!==i.name&&"text"!==i.name||g(),"textPath"===i.name){h.push(i);let t=i.pathObject;t&&(l=t.endPoint[0],c=t.endPoint[1])}u&&(u._pos=u._pos.concat(i._pos),u._index+=i._index)}(i,null);for(let t=0;t<h.length;t++)u(h[t])}(this),this.getTransformation=function(){return this.get("transform")},this.drawInDocument=function(e,i){let n;t.save(),this.transform(),this.clip(),this.mask()&&(n=g(C())),this.drawTextInDocument(e,i),n&&(u(n),d(n)),t.restore()}},Lt=(s=s||{}).assumePt?1:.75,Pt=(s.width||t.page.width)/Lt,Vt=(s.height||t.page.height)/Lt,Ct=s.preserveAspectRatio||null,_t=s.useCSS&&"undefined"!=typeof SVGElement&&e instanceof SVGElement&&"function"==typeof getComputedStyle,St=s.warningCallback,Bt=s.fontCallback,At=s.imageCallback,It=s.colorCallback,Ot=s.documentCallback,Tt=Math.ceil(Math.max(1,s.precision))||3,Ht=[],Nt={},Wt=[],qt=[];if("function"!=typeof St&&(St=function(t){void 0!==typeof console&&"function"==typeof console.warn&&console.warn(t)}),"function"!=typeof Bt&&(Bt=function(e,i,n,s){if(i&&n){if(t._registeredFonts.hasOwnProperty(e+"-BoldItalic"))return e+"-BoldItalic";if(t._registeredFonts.hasOwnProperty(e+"-Italic"))return s.fauxBold=!0,e+"-Italic";if(t._registeredFonts.hasOwnProperty(e+"-Bold"))return s.fauxItalic=!0,e+"-Bold";if(t._registeredFonts.hasOwnProperty(e))return s.fauxBold=!0,s.fauxItalic=!0,e}if(i&&!n){if(t._registeredFonts.hasOwnProperty(e+"-Bold"))return e+"-Bold";if(t._registeredFonts.hasOwnProperty(e))return s.fauxBold=!0,e}if(!i&&n){if(t._registeredFonts.hasOwnProperty(e+"-Italic"))return e+"-Italic";if(t._registeredFonts.hasOwnProperty(e))return s.fauxItalic=!0,e}if(!i&&!n&&t._registeredFonts.hasOwnProperty(e))return e;if(e.match(/(?:^|,)\s*serif\s*$/)){if(i&&n)return"Times-BoldItalic";if(i&&!n)return"Times-Bold";if(!i&&n)return"Times-Italic";if(!i&&!n)return"Times-Roman"}else if(e.match(/(?:^|,)\s*monospace\s*$/)){if(i&&n)return"Courier-BoldOblique";if(i&&!n)return"Courier-Bold";if(!i&&n)return"Courier-Oblique";if(!i&&!n)return"Courier"}else{if(e.match(/(?:^|,)\s*sans-serif\s*$/),i&&n)return"Helvetica-BoldOblique";if(i&&!n)return"Helvetica-Bold";if(!i&&n)return"Helvetica-Oblique";if(!i&&!n)return"Helvetica"}}),"function"!=typeof At&&(At=function(t){return t.replace(/\s+/g,"")}),"function"!=typeof It)It=null;else for(let t in r){let e=It(r[t]);r[t][0]=e[0],r[t][1]=e[1]}if("function"!=typeof Ot&&(Ot=null),"string"==typeof e&&(e=x(e)),e){let a=e.getElementsByTagName("style");for(let t=0;t<a.length;t++)qt=qt.concat(z(a[t].textContent));let r=G(e,null);if("function"==typeof r.drawInDocument){s.useCSS&&!_t&&St("SVGtoPDF: useCSS option can only be used for SVG *elements* in compatible browsers"),t.save().translate(i||0,n||0).scale(Lt),r.drawInDocument();for(let e=0;e<Wt.length;e++)t.page.annotations.push(Wt[e]);t.restore()}else St("SVGtoPDF: this element can't be rendered directly: "+e.nodeName)}else St("SVGtoPDF: the input does not look like a valid SVG")};"undefined"!=typeof module&&module&&void 0!==module.exports&&(module.exports=SVGtoPDF);