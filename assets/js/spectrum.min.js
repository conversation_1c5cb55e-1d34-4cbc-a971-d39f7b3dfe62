!function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports&&"object"==typeof module?module.exports=t(require("jquery")):t(jQuery)}(function(t,e){"use strict";var a={beforeShow:u,move:u,change:u,show:u,hide:u,color:!1,flat:!1,showInput:!1,allowEmpty:!1,showButtons:!1,showCMYK:!0,clickoutFiresChange:!0,showInitial:!1,showPalette:!1,showPaletteOnly:!1,hideAfterPaletteSelect:!1,togglePaletteOnly:!1,showSelectionPalette:!0,localStorageKey:!1,appendTo:"body",maxSelectionSize:0,cancelText:"cancel",chooseText:"choose",togglePaletteMoreText:"more",togglePaletteLessText:"less",clearText:"Clear Color Selection",noColorSelectedText:"No Color Selected",preferredFormat:!1,className:"",containerClassName:"",replacerClassName:"",showAlpha:!1,theme:"sp-light",palette:[["#ffffff","#000000","#ff0000"]],paletteCMYK:[["0,0,0,0","0,0,0,100","0,100,100,0"]],selectionPalette:[],disabled:!1,offset:null},r=[],n=!!/msie/i.exec(window.navigator.userAgent),i=function(){function t(t,e){return!!~(""+t).indexOf(e)}var e=document.createElement("div").style;return e.cssText="background-color:rgba(0,0,0,.5)",t(e.backgroundColor,"rgba")||t(e.backgroundColor,"hsla")}(),s=["<div class='sp-replacer'>","<div class='sp-preview'><div class='sp-preview-inner'></div></div>","<div class='sp-dd'>&#9660;</div>","</div>"].join(""),o=function(){var t="";if(n)for(var e=1;e<=6;e++)t+="<div class='sp-"+e+"'></div>";return["<div class='sp-container sp-hidden'>","<div class='sp-palette-container'>","<div class='sp-palette sp-thumb sp-cf'></div>","<div class='sp-palette-button-container sp-cf'>","<button type='button' class='sp-palette-toggle'></button>","</div>","</div>","<div class='sp-picker-container'>","<div class='sp-top sp-cf'>","<div class='sp-fill'></div>","<div class='sp-top-inner'>","<div class='sp-color'>","<div class='sp-sat'>","<div class='sp-val'>","<div class='sp-dragger'></div>","</div>","</div>","</div>","<div class='sp-clear sp-clear-display'>","</div>","<div class='sp-hue'>","<div class='sp-slider'></div>",t,"</div>","</div>","<div class='sp-alpha'><div class='sp-alpha-inner'><div class='sp-alpha-handle'></div></div></div>","</div>","<div class='sp-input-container sp-cf'>","<input class='sp-input' type='text' spellcheck='false'  />","</div>","<div class='sp-initial sp-thumb sp-cf'></div>","<div class='sp-button-container sp-cf'>","<a class='sp-cancel' href='#'></a>","<button type='button' class='sp-choose'></button>","</div>","<div class='sp-cmyk-container'>","<span><label>C</label><input type='text' class='CyanPrc' name='CyanPrc' size='3' maxlength='3'></span>","<span><label>M</label><input type='text' class='MagentaPrc' name='MagentaPrc' size='3' maxlength='3'></span>","<span><label>Y</label><input type='text' class='YellowPrc' name='YellowPrc' size='3' maxlength='3'></span>","<span><label>K</label><input type='text' class='BlackPrc' name='BlackPrc' size='3' maxlength='3'></span>","</div>","</div>","</div>"].join("")}();function l(e,a,r,n,s){for(var o=[],l=0;l<e.length;l++){var c=e[l];if(c){var u=tinycolor(c),f=u.toHsl().l<.5?"sp-thumb-el sp-thumb-dark":"sp-thumb-el sp-thumb-light";f+=tinycolor.equals(a,c)?" sp-thumb-active":"";u.toString(n.preferredFormat||"rgb");var h=i?"background-color:"+u.toRgbString():"filter:"+u.toFilter();o.push('<span data-color="'+u.toRgbString()+'" data-cmyk="'+s[l]+'" class="'+f+'"><span class="sp-thumb-inner" style="'+h+';"></span></span>')}else{o.push(t("<div />").append(t('<span data-color="" style="background-color:transparent;" class="sp-clear-display"></span>').attr("title",n.noColorSelectedText)).html())}}return"<div class='sp-cf "+r+"'>"+o.join("")+"</div>"}function c(c,u){var g,b,v,m,y,w,_,k=(y=u,w=c,(_=t.extend({},a,y)).callbacks={move:h(_.move,w),change:h(_.change,w),show:h(_.show,w),hide:h(_.hide,w),beforeShow:h(_.beforeShow,w)},_),x=k.flat,C=k.showSelectionPalette,M=k.localStorageKey,S=k.theme,P=k.callbacks,A=(g=le,b=10,function(){var t=this,e=arguments;v&&clearTimeout(m),!v&&m||(m=setTimeout(function(){m=null,g.apply(t,e)},b))}),R=!1,H=!1,F=0,T=0,Y=0,O=0,B=0,K=0,I=0,N=0,j=0,q=0,D=1,E=[],z=[],L={},V=k.selectionPalette.slice(0),$=k.maxSelectionSize,G="sp-dragging",W=null,X=c.ownerDocument,U=(X.body,t(c)),Q=!1,J=t(o,X).addClass(S),Z=J.find(".sp-picker-container"),tt=U.attr("callback"),et=U.attr("id"),at=U.parent().find("input[type='hidden']"),rt=U.attr("cmyk"),nt=J.find(".CyanPrc"),it=J.find(".MagentaPrc"),st=J.find(".YellowPrc"),ot=J.find(".BlackPrc"),lt={Cyan:0,Magenta:0,Yellow:0,Black:0},ct=!1,ut=J.find(".sp-color"),ft=J.find(".sp-dragger"),ht=J.find(".sp-hue"),pt=J.find(".sp-slider"),dt=J.find(".sp-alpha-inner"),gt=J.find(".sp-alpha"),bt=J.find(".sp-alpha-handle"),vt=J.find(".sp-input"),mt=J.find(".sp-palette"),yt=J.find(".sp-initial"),wt=J.find(".sp-cancel"),_t=J.find(".sp-clear"),kt=J.find(".sp-choose"),xt=J.find(".sp-palette-toggle"),Ct=U.is("input"),Mt=Ct&&"color"===U.attr("type")&&d(),St=Ct&&!x,Pt=St?t(s).addClass(S).addClass(k.className).addClass(k.replacerClassName):t([]),At=St?Pt:U,Rt=Pt.find(".sp-preview-inner"),Ht=k.color||Ct&&U.val(),Ft=!1,Tt=k.preferredFormat,Yt=!k.showButtons||k.clickoutFiresChange,Ot=!Ht,Bt=k.allowEmpty&&!Mt;function Kt(){if(k.showPaletteOnly&&(k.showPalette=!0),xt.text(k.showPaletteOnly?k.togglePaletteMoreText:k.togglePaletteLessText),k.palette){E=k.palette.slice(0),z=t.isArray(E[0])?E:[E],L={};for(var e=0;e<z.length;e++)for(var a=0;a<z[e].length;a++){var r=tinycolor(z[e][a]).toRgbString();L[r]=!0}}J.toggleClass("sp-flat",x),J.toggleClass("sp-input-disabled",!k.showInput),J.toggleClass("sp-alpha-enabled",k.showAlpha),J.toggleClass("sp-clear-enabled",Bt),J.toggleClass("sp-buttons-disabled",!k.showButtons),J.toggleClass("sp-palette-buttons-disabled",!k.togglePaletteOnly),J.toggleClass("sp-palette-disabled",!k.showPalette),J.toggleClass("sp-palette-only",k.showPaletteOnly),J.toggleClass("sp-initial-disabled",!k.showInitial),J.toggleClass("sp-cmyk-disabled",!k.showCMYK),J.addClass(k.className).addClass(k.containerClassName),le()}function It(t,e){if(!(isNaN(t)||t>100||(t=Math.round(t),lt[e]==t))){lt[e]=t;var a=lt.Cyan+","+lt.Magenta+","+lt.Yellow+","+lt.Black;U.attr("cmyk",a),tt||at.val(a),jt(a,wcdp_spectrum_functions[tt],et,1),ct=!1}}function Nt(t){var e=ae().toHexString().replace("#",""),a=lt.Cyan+","+lt.Magenta+","+lt.Yellow+","+lt.Black;tt?t?jt(e,wcdp_spectrum_functions[tt],et):(k.showCMYK||(a="0,0,0,0"),wcdp_spectrum_functions[tt](e,a,et)):t?jt(e):k.showCMYK&&at.val(a)}function jt(t,e,a,r){var n,i,s,o,l,c,u,f,h,p,d,g="",b="";if(k.showCMYK){if(r){g=t;var v=t.split(",");l=v[0],c=v[1],u=v[2],f=v[3],h=1-(l=(l/=100)*(1-(f/=100))+f),p=1-(c=(c/=100)*(1-f)+f),d=1-(u=(u/=100)*(1-f)+f),h=Math.round(255*h),p=Math.round(255*p),d=Math.round(255*d),b={r:h,g:p,b:d}}else{var m=function(t){var e=new ArrayBuffer(4);new DataView(e).setUint32(0,parseInt(t,16),!1);var a=new Uint8Array(e),r=0,n=0,i=0,s=0,o=a[1]/255,l=a[2]/255,c=a[3]/255;s=Math.min(1-o,1-l,1-c),r=(1-o-s)/(1-s),n=(1-l-s)/(1-s),i=(1-c-s)/(1-s),r=Math.round(100*r),n=Math.round(100*n),i=Math.round(100*i),s=Math.round(100*s),0==o&&0==l&&0==c&&(r=0,n=0,i=0,s=100);return{Cyan:r,Magenta:n,Yellow:i,Black:s}}(t);lt=m,g=m.Cyan+","+m.Magenta+","+m.Yellow+","+m.Black,b=tinycolor(t).toRgb()}var y=(n=b,i=Math.round(n.r/17).toString(16),s=Math.round(n.g/17).toString(16),o=Math.round(n.b/17).toString(16),i+""+s+o),w=parseInt(y,16),_=wcdp_picker_table[w];"151616"==_&&(_="000000"),r?Zt(_):(te(g),ee(_)),e&&e(_,g,a)}else e&&e(t,"0,0,0,0",a)}function qt(){if(M&&window.localStorage){try{var e=window.localStorage[M].split(",#");e.length>1&&(delete window.localStorage[M],t.each(e,function(t,e){Dt(e)}))}catch(t){}try{V=window.localStorage[M].split(";")}catch(t){}}}function Dt(e){if(C){var a=tinycolor(e).toRgbString();if(!L[a]&&-1===t.inArray(a,V))for(V.push(a);V.length>$;)V.shift();if(M&&window.localStorage)try{window.localStorage[M]=V.join(";")}catch(t){}}}function Et(){var e=ae(),a=t.map(z,function(t,a,r){return l(t,e,"sp-palette-row sp-palette-row-"+a,k,k.paletteCMYK[a])});qt(),V&&a.push(l(function(){var t=[];if(k.showPalette)for(var e=0;e<V.length;e++){var a=tinycolor(V[e]).toRgbString();L[a]||t.push(V[e])}return t.reverse().slice(0,k.maxSelectionSize)}(),e,"sp-palette-row sp-palette-row-selection",k,[])),mt.html(a.join(""))}function zt(){if(k.showInitial){var t=Ft,e=ae();yt.html(l([t,e],e,"sp-palette-row-initial",k))}}function Lt(){(T<=0||F<=0||O<=0)&&le(),H=!0,J.addClass(G),W=null,U.trigger("dragstart.spectrum",[ae()])}function Vt(){H=!1,J.removeClass(G),U.trigger("dragstop.spectrum",[ae()]),Nt("compRGB")}function $t(){var t=vt.val();if(null!==t&&""!==t||!Bt){var e=tinycolor(t);e.isValid()?(Zt(e),se(!0)):vt.addClass("sp-validation-error")}else Zt(null),se(!0)}function Gt(){R?Qt():Wt()}function Wt(){t(".wcdp_overlay_loader").css("cursor","pointer").show();var e=t.Event("beforeShow.spectrum");R?le():(U.trigger(e,[ae()]),!1===P.beforeShow(ae())||e.isDefaultPrevented()||(!function(){for(var t=0;t<r.length;t++)r[t]&&r[t].hide()}(),R=!0,t(X).bind("keydown.spectrum",Xt),t(X).bind("click.spectrum",Ut),t(window).bind("resize.spectrum",A),Pt.addClass("sp-active"),J.removeClass("sp-hidden").addClass("sp-contain-active"),le(),ne(),Ft=ae(),zt(),P.show(Ft),U.trigger("show.spectrum",[Ft])))}function Xt(t){27===t.keyCode&&Qt()}function Ut(t){2!=t.button&&(H||(Yt?se(!0):Jt(),Qt()))}function Qt(){k.showCMYK&&1==ct&&(Zt(Rt.css("background-color")),U.val(ae().toHexString()),ct=!1),R&&!x&&(R=!1,t(X).unbind("keydown.spectrum",Xt),t(X).unbind("click.spectrum",Ut),t(window).unbind("resize.spectrum",A),Pt.removeClass("sp-active"),J.addClass("sp-hidden").removeClass("sp-contain-active"),P.hide(ae()),U.trigger("hide.spectrum",[ae()]),tt&&wcdp_spectrum_functions.saveState(),t(".wcdp_overlay_loader").css("cursor","default").hide())}function Jt(){Zt(Ft,!0)}function Zt(t,e){var a,r;tinycolor.equals(t,ae())?ne():(!t&&Bt?Ot=!0:(Ot=!1,r=(a=tinycolor(t)).toHsv(),N=r.h%360/360,j=r.s,q=r.v,D=r.a),ne(),a&&a.isValid()&&!e&&(Tt=k.preferredFormat||a.getFormat()))}function te(t){oe(t.split(",")),tt||at.val(t)}function ee(t){ct=!0,k.showInput&&vt.val("#"+t),Rt.css("background-color","#"+t)}function ae(t){return t=t||{},Bt&&Ot?null:tinycolor.fromRatio({h:N,s:j,v:q,a:Math.round(100*D)/100},{format:t.format||Tt})}function re(){ne(),P.move(ae()),U.trigger("move.spectrum",[ae()])}function ne(){vt.removeClass("sp-validation-error"),ie();var t=tinycolor.fromRatio({h:N,s:1,v:1});ut.css("background-color",t.toHexString());var e=Tt;D<1&&(0!==D||"name"!==e)&&("hex"!==e&&"hex3"!==e&&"hex6"!==e&&"name"!==e||(e="rgb"));var a=ae({format:e}),r="";if(Rt.removeClass("sp-clear-display"),Rt.css("background-color","transparent"),!a&&Bt)Rt.addClass("sp-clear-display");else{var s=a.toHexString(),o=a.toRgbString();if(i||1===a.alpha?Rt.css("background-color",o):(Rt.css("background-color","transparent"),Rt.css("filter",a.toFilter())),k.showAlpha){var l=a.toRgb();l.a=0;var c=tinycolor(l).toRgbString(),u="linear-gradient(left, "+c+", "+s+")";n?dt.css("filter",tinycolor(c).toFilter({gradientType:1},s)):(dt.css("background","-webkit-"+u),dt.css("background","-moz-"+u),dt.css("background","-ms-"+u),dt.css("background","linear-gradient(to right, "+c+", "+s+")"))}r=a.toString(e)}k.showInput&&vt.val(r),k.showPalette&&Et(),zt()}function ie(){var t=j,e=q;if(Bt&&Ot)bt.hide(),pt.hide(),ft.hide();else{bt.show(),pt.show(),ft.show();var a=t*F,r=T-e*T;a=Math.max(-Y,Math.min(F-Y,a-Y)),r=Math.max(-Y,Math.min(T-Y,r-Y)),ft.css({top:r+"px",left:a+"px"});var n=D*B;bt.css({left:n-K/2+"px"});var i=N*O;pt.css({top:i-I+"px"})}}function se(t){var e=ae(),a="",r=!tinycolor.equals(e,Ft);e&&(a=e.toString(Tt),Dt(e)),Ct&&U.val(a),t&&r&&(P.change(e),U.trigger("change",[e]),k.showCMYK||void 0===tt||wcdp_spectrum_functions[tt](a.replace("#",""),"0,0,0,0",et))}function oe(t){k.showCMYK&&(nt.val(t[0]),it.val(t[1]),st.val(t[2]),ot.val(t[3]),U.attr("cmyk",t.join()),lt={Cyan:t[0],Magenta:t[1],Yellow:t[2],Black:t[3]})}function le(){var e,a,r,n,i,s,o,l,c,u;R&&(F=ut.width(),T=ut.height(),Y=ft.height(),ht.width(),O=ht.height(),I=pt.height(),B=gt.width(),K=bt.width(),x||(J.css("position","absolute"),k.offset?J.offset(k.offset):J.offset((a=At,r=(e=J).outerWidth(),n=e.outerHeight(),i=a.outerHeight(),s=e[0].ownerDocument,o=s.documentElement,l=o.clientWidth+t(s).scrollLeft(),c=o.clientHeight+t(s).scrollTop(),(u=a.offset()).top+=i,u.left-=Math.min(u.left,u.left+r>l&&l>r?Math.abs(u.left+r-l):0),u.top-=Math.min(u.top,u.top+n>c&&c>n?Math.abs(n+i-0):0),u))),ie(),k.showPalette&&Et(),U.trigger("reflow.spectrum"))}function ce(){Qt(),Q=!0,U.attr("disabled",!0),At.addClass("sp-disabled")}!function(){if(void 0!==rt&&oe(rt.split(",")),nt.keyup(function(){It(this.value,"Cyan")}),it.keyup(function(){It(this.value,"Magenta")}),st.keyup(function(){It(this.value,"Yellow")}),ot.keyup(function(){It(this.value,"Black")}),n&&J.find("*:not(input)").attr("unselectable","on"),Kt(),St&&U.after(Pt).hide(),Bt||_t.hide(),x)U.after(J).hide();else{var e="parent"===k.appendTo?U.parent():t(k.appendTo);1!==e.length&&(e=t("body")),e.append(J)}function a(e){e.data&&e.data.ignore?(Zt(t(e.target).closest(".sp-thumb-el").data("color")),re()):(Zt(t(e.target).closest(".sp-thumb-el").data("color")),re(),se(!0),k.hideAfterPaletteSelect&&Qt());var a=t(this).attr("data-cmyk").split(",");return lt={Cyan:a[0],Magenta:a[1],Yellow:a[2],Black:a[3]},oe(a),Nt(),ct=!1,!1}qt(),At.bind("click.spectrum touchstart.spectrum",function(e){Q||Gt(),e.stopPropagation(),t(e.target).is("input")||e.preventDefault()}),(U.is(":disabled")||!0===k.disabled)&&ce(),J.click(f),vt.change($t),vt.bind("paste",function(){setTimeout($t,1)}),vt.keydown(function(t){13==t.keyCode&&$t()}),wt.text(k.cancelText),wt.bind("click.spectrum",function(t){t.stopPropagation(),t.preventDefault(),Jt(),Qt()}),_t.attr("title",k.clearText),_t.bind("click.spectrum",function(t){t.stopPropagation(),t.preventDefault(),Ot=!0,re(),x&&se(!0)}),kt.text(k.chooseText),kt.bind("click.spectrum",function(t){t.stopPropagation(),t.preventDefault(),n&&vt.is(":focus")&&vt.trigger("change"),vt.hasClass("sp-validation-error")||(se(!0),Qt())}),xt.text(k.showPaletteOnly?k.togglePaletteMoreText:k.togglePaletteLessText),xt.bind("click.spectrum",function(t){t.stopPropagation(),t.preventDefault(),k.showPaletteOnly=!k.showPaletteOnly,k.showPaletteOnly||x||J.css("left","-="+(Z.outerWidth(!0)+5)),Kt()}),p(gt,function(t,e,a){D=t/B,Ot=!1,a.shiftKey&&(D=Math.round(10*D)/10),re()},Lt,Vt),p(ht,function(t,e){N=parseFloat(e/O),Ot=!1,k.showAlpha||(D=1),re()},Lt,Vt),p(ut,function(t,e,a){if(a.shiftKey){if(!W){var r=j*F,n=T-q*T,i=Math.abs(t-r)>Math.abs(e-n);W=i?"x":"y"}}else W=null;var s=!W||"y"===W;(!W||"x"===W)&&(j=parseFloat(t/F)),s&&(q=parseFloat((T-e)/T)),Ot=!1,k.showAlpha||(D=1),re()},Lt,Vt),Ht?(Zt(Ht),ne(),Tt=k.preferredFormat||tinycolor(Ht).format,Dt(Ht)):ne(),x&&Wt();var r=n?"mousedown.spectrum":"click.spectrum touchstart.spectrum";mt.delegate(".sp-thumb-el",r,a),yt.delegate(".sp-thumb-el:nth-child(1)",r,{ignore:!0},a)}();var ue={show:Wt,hide:Qt,toggle:Gt,reflow:le,option:function(a,r){return a===e?t.extend({},k):r===e?k[a]:(k[a]=r,"preferredFormat"===a&&(Tt=k.preferredFormat),void Kt())},enable:function(){Q=!1,U.attr("disabled",!1),At.removeClass("sp-disabled")},disable:ce,offset:function(t){k.offset=t,le()},set:function(t){Zt(t),se()},setCMYK:te,recall:ee,get:ae,destroy:function(){U.show(),At.unbind("click.spectrum touchstart.spectrum"),J.remove(),Pt.remove(),r[ue.id]=null},container:J};return ue.id=r.push(ue)-1,ue}function u(){}function f(t){t.stopPropagation()}function h(t,e){var a=Array.prototype.slice,r=a.call(arguments,2);return function(){return t.apply(e,r.concat(a.call(arguments)))}}function p(e,a,r,i){a=a||function(){},r=r||function(){},i=i||function(){};var s=document,o=!1,l={},c=0,u=0,f="ontouchstart"in window,h={};function p(t){t.stopPropagation&&t.stopPropagation(),t.preventDefault&&t.preventDefault(),t.returnValue=!1}function d(t){if(o){if(n&&s.documentMode<9&&!t.button)return g();var r=t.originalEvent&&t.originalEvent.touches&&t.originalEvent.touches[0],i=r&&r.pageX||t.pageX,h=r&&r.pageY||t.pageY,d=Math.max(0,Math.min(i-l.left,u)),b=Math.max(0,Math.min(h-l.top,c));f&&p(t),a.apply(e,[d,b,t])}}function g(){o&&(t(s).unbind(h),t(s.body).removeClass("sp-dragging"),setTimeout(function(){i.apply(e,arguments)},0)),o=!1}h.selectstart=p,h.dragstart=p,h["touchmove mousemove"]=d,h["touchend mouseup"]=g,t(e).bind("touchstart mousedown",function(a){(a.which?3==a.which:2==a.button)||o||!1!==r.apply(e,arguments)&&(o=!0,c=t(e).height(),u=t(e).width(),l=t(e).offset(),t(s).bind(h),t(s.body).addClass("sp-dragging"),d(a),p(a))})}function d(){return t.fn.spectrum.inputTypeColorSupport()}var g="spectrum.id";t.fn.spectrum=function(e,a){if("string"==typeof e){var n=this,i=Array.prototype.slice.call(arguments,1);return this.each(function(){var a=r[t(this).data(g)];if(a){var s=a[e];if(!s)throw new Error("Spectrum: no such method: '"+e+"'");"get"==e?n=a.get():"container"==e?n=a.container:"option"==e?n=a.option.apply(a,i):"destroy"==e?(a.destroy(),t(this).removeData(g)):s.apply(a,i)}}),n}return this.spectrum("destroy").each(function(){var a=c(this,t.extend({},e,t(this).data()));t(this).data(g,a.id)})},t.fn.spectrum.load=!0,t.fn.spectrum.loadOpts={},t.fn.spectrum.draggable=p,t.fn.spectrum.defaults=a,t.fn.spectrum.inputTypeColorSupport=function e(){if(void 0===e._cachedResult){var a=t("<input type='color'/>")[0];e._cachedResult="color"===a.type&&""!==a.value}return e._cachedResult},t.spectrum={},t.spectrum.localization={},t.spectrum.palettes={},t.spectrum.installPicker=function(e){var a="",r="",n="",i=!0,s=!1,o=wcdp_style.picker_palette,l=parseInt(wcdp_style.column_picker_palette);a="on"==wcdp_style.show_picker_palette,"on"==wcdp_settings.CMYK&&"on"==wcdp_settings.cmyk_picker&&(s=!0,i=!1),void 0!==o&&(r=t.spectrum.array_chunk(l,o.RGB),n=t.spectrum.array_chunk(l,o.CMYK)),t("."+e).spectrum({preferredFormat:"hex",showInput:i,showPalette:a,showCMYK:s,palette:r,paletteCMYK:n})},t.spectrum.array_chunk=function(t,e){return e.reduce(function(e,a){var r;return 0===e.length||e[e.length-1].length===t?(r=[],e.push(r)):r=e[e.length-1],r.push(a),e},[])},t.fn.spectrum.processNativeColorInputs=function(){var e=t("input[type=color]");e.length&&!d()&&e.spectrum({preferredFormat:"hex6"})},function(){var t=/^[\s,#]+/,e=/\s+$/,a=0,r=Math,n=r.round,i=r.min,s=r.max,o=r.random,l=function(o,c){if(o=o||"",c=c||{},o instanceof l)return o;if(!(this instanceof l))return new l(o,c);var u=function(a){var n={r:0,g:0,b:0},o=1,l=!1,c=!1;"string"==typeof a&&(a=function(a){a=a.replace(t,"").replace(e,"").toLowerCase();var r,n=!1;if(S[a])a=S[a],n=!0;else if("transparent"==a)return{r:0,g:0,b:0,a:0,format:"name"};if(r=I.rgb.exec(a))return{r:r[1],g:r[2],b:r[3]};if(r=I.rgba.exec(a))return{r:r[1],g:r[2],b:r[3],a:r[4]};if(r=I.hsl.exec(a))return{h:r[1],s:r[2],l:r[3]};if(r=I.hsla.exec(a))return{h:r[1],s:r[2],l:r[3],a:r[4]};if(r=I.hsv.exec(a))return{h:r[1],s:r[2],v:r[3]};if(r=I.hsva.exec(a))return{h:r[1],s:r[2],v:r[3],a:r[4]};if(r=I.hex8.exec(a))return{a:(i=r[1],F(i)/255),r:F(r[2]),g:F(r[3]),b:F(r[4]),format:n?"name":"hex8"};var i;if(r=I.hex6.exec(a))return{r:F(r[1]),g:F(r[2]),b:F(r[3]),format:n?"name":"hex"};if(r=I.hex3.exec(a))return{r:F(r[1]+""+r[1]),g:F(r[2]+""+r[2]),b:F(r[3]+""+r[3]),format:n?"name":"hex"};return!1}(a));"object"==typeof a&&(a.hasOwnProperty("r")&&a.hasOwnProperty("g")&&a.hasOwnProperty("b")?(u=a.r,f=a.g,h=a.b,n={r:255*R(u,255),g:255*R(f,255),b:255*R(h,255)},l=!0,c="%"===String(a.r).substr(-1)?"prgb":"rgb"):a.hasOwnProperty("h")&&a.hasOwnProperty("s")&&a.hasOwnProperty("v")?(a.s=Y(a.s),a.v=Y(a.v),n=function(t,e,a){t=6*R(t,360),e=R(e,100),a=R(a,100);var n=r.floor(t),i=t-n,s=a*(1-e),o=a*(1-i*e),l=a*(1-(1-i)*e),c=n%6;return{r:255*[a,o,s,s,l,a][c],g:255*[l,a,a,o,s,s][c],b:255*[s,s,l,a,a,o][c]}}(a.h,a.s,a.v),l=!0,c="hsv"):a.hasOwnProperty("h")&&a.hasOwnProperty("s")&&a.hasOwnProperty("l")&&(a.s=Y(a.s),a.l=Y(a.l),n=function(t,e,a){var r,n,i;function s(t,e,a){return a<0&&(a+=1),a>1&&(a-=1),a<1/6?t+6*(e-t)*a:a<.5?e:a<2/3?t+(e-t)*(2/3-a)*6:t}if(t=R(t,360),e=R(e,100),a=R(a,100),0===e)r=n=i=a;else{var o=a<.5?a*(1+e):a+e-a*e,l=2*a-o;r=s(l,o,t+1/3),n=s(l,o,t),i=s(l,o,t-1/3)}return{r:255*r,g:255*n,b:255*i}}(a.h,a.s,a.l),l=!0,c="hsl"),a.hasOwnProperty("a")&&(o=a.a));var u,f,h;return o=A(o),{ok:l,format:a.format||c,r:i(255,s(n.r,0)),g:i(255,s(n.g,0)),b:i(255,s(n.b,0)),a:o}}(o);this._originalInput=o,this._r=u.r,this._g=u.g,this._b=u.b,this._a=u.a,this._roundA=n(100*this._a)/100,this._format=c.format||u.format,this._gradientType=c.gradientType,this._r<1&&(this._r=n(this._r)),this._g<1&&(this._g=n(this._g)),this._b<1&&(this._b=n(this._b)),this._ok=u.ok,this._tc_id=a++};function c(t,e,a){t=R(t,255),e=R(e,255),a=R(a,255);var r,n,o=s(t,e,a),l=i(t,e,a),c=(o+l)/2;if(o==l)r=n=0;else{var u=o-l;switch(n=c>.5?u/(2-o-l):u/(o+l),o){case t:r=(e-a)/u+(e<a?6:0);break;case e:r=(a-t)/u+2;break;case a:r=(t-e)/u+4}r/=6}return{h:r,s:n,l:c}}function u(t,e,a){t=R(t,255),e=R(e,255),a=R(a,255);var r,n,o=s(t,e,a),l=i(t,e,a),c=o,u=o-l;if(n=0===o?0:u/o,o==l)r=0;else{switch(o){case t:r=(e-a)/u+(e<a?6:0);break;case e:r=(a-t)/u+2;break;case a:r=(t-e)/u+4}r/=6}return{h:r,s:n,v:c}}function f(t,e,a,r){var i=[T(n(t).toString(16)),T(n(e).toString(16)),T(n(a).toString(16))];return r&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function h(t,e,a,r){var i;return[T((i=r,Math.round(255*parseFloat(i)).toString(16))),T(n(t).toString(16)),T(n(e).toString(16)),T(n(a).toString(16))].join("")}function p(t,e){e=0===e?0:e||10;var a=l(t).toHsl();return a.s-=e/100,a.s=H(a.s),l(a)}function d(t,e){e=0===e?0:e||10;var a=l(t).toHsl();return a.s+=e/100,a.s=H(a.s),l(a)}function g(t){return l(t).desaturate(100)}function b(t,e){e=0===e?0:e||10;var a=l(t).toHsl();return a.l+=e/100,a.l=H(a.l),l(a)}function v(t,e){e=0===e?0:e||10;var a=l(t).toRgb();return a.r=s(0,i(255,a.r-n(-e/100*255))),a.g=s(0,i(255,a.g-n(-e/100*255))),a.b=s(0,i(255,a.b-n(-e/100*255))),l(a)}function m(t,e){e=0===e?0:e||10;var a=l(t).toHsl();return a.l-=e/100,a.l=H(a.l),l(a)}function y(t,e){var a=l(t).toHsl(),r=(n(a.h)+e)%360;return a.h=r<0?360+r:r,l(a)}function w(t){var e=l(t).toHsl();return e.h=(e.h+180)%360,l(e)}function _(t){var e=l(t).toHsl(),a=e.h;return[l(t),l({h:(a+120)%360,s:e.s,l:e.l}),l({h:(a+240)%360,s:e.s,l:e.l})]}function k(t){var e=l(t).toHsl(),a=e.h;return[l(t),l({h:(a+90)%360,s:e.s,l:e.l}),l({h:(a+180)%360,s:e.s,l:e.l}),l({h:(a+270)%360,s:e.s,l:e.l})]}function x(t){var e=l(t).toHsl(),a=e.h;return[l(t),l({h:(a+72)%360,s:e.s,l:e.l}),l({h:(a+216)%360,s:e.s,l:e.l})]}function C(t,e,a){e=e||6,a=a||30;var r=l(t).toHsl(),n=360/a,i=[l(t)];for(r.h=(r.h-(n*e>>1)+720)%360;--e;)r.h=(r.h+n)%360,i.push(l(r));return i}function M(t,e){e=e||6;for(var a=l(t).toHsv(),r=a.h,n=a.s,i=a.v,s=[],o=1/e;e--;)s.push(l({h:r,s:n,v:i})),i=(i+o)%1;return s}l.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},setAlpha:function(t){return this._a=A(t),this._roundA=n(100*this._a)/100,this},toHsv:function(){var t=u(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=u(this._r,this._g,this._b),e=n(360*t.h),a=n(100*t.s),r=n(100*t.v);return 1==this._a?"hsv("+e+", "+a+"%, "+r+"%)":"hsva("+e+", "+a+"%, "+r+"%, "+this._roundA+")"},toHsl:function(){var t=c(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=c(this._r,this._g,this._b),e=n(360*t.h),a=n(100*t.s),r=n(100*t.l);return 1==this._a?"hsl("+e+", "+a+"%, "+r+"%)":"hsla("+e+", "+a+"%, "+r+"%, "+this._roundA+")"},toHex:function(t){return f(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(){return h(this._r,this._g,this._b,this._a)},toHex8String:function(){return"#"+this.toHex8()},toRgb:function(){return{r:n(this._r),g:n(this._g),b:n(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+n(this._r)+", "+n(this._g)+", "+n(this._b)+")":"rgba("+n(this._r)+", "+n(this._g)+", "+n(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:n(100*R(this._r,255))+"%",g:n(100*R(this._g,255))+"%",b:n(100*R(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+n(100*R(this._r,255))+"%, "+n(100*R(this._g,255))+"%, "+n(100*R(this._b,255))+"%)":"rgba("+n(100*R(this._r,255))+"%, "+n(100*R(this._g,255))+"%, "+n(100*R(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(P[f(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var e="#"+h(this._r,this._g,this._b,this._a),a=e,r=this._gradientType?"GradientType = 1, ":"";t&&(a=l(t).toHex8String());return"progid:DXImageTransform.Microsoft.gradient("+r+"startColorstr="+e+",endColorstr="+a+")"},toString:function(t){var e=!!t;t=t||this._format;var a=!1,r=this._a<1&&this._a>=0;return e||!r||"hex"!==t&&"hex6"!==t&&"hex3"!==t&&"name"!==t?("rgb"===t&&(a=this.toRgbString()),"prgb"===t&&(a=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(a=this.toHexString()),"hex3"===t&&(a=this.toHexString(!0)),"hex8"===t&&(a=this.toHex8String()),"name"===t&&(a=this.toName()),"hsl"===t&&(a=this.toHslString()),"hsv"===t&&(a=this.toHsvString()),a||this.toHexString()):"name"===t&&0===this._a?this.toName():this.toRgbString()},_applyModification:function(t,e){var a=t.apply(null,[this].concat([].slice.call(e)));return this._r=a._r,this._g=a._g,this._b=a._b,this.setAlpha(a._a),this},lighten:function(){return this._applyModification(b,arguments)},brighten:function(){return this._applyModification(v,arguments)},darken:function(){return this._applyModification(m,arguments)},desaturate:function(){return this._applyModification(p,arguments)},saturate:function(){return this._applyModification(d,arguments)},greyscale:function(){return this._applyModification(g,arguments)},spin:function(){return this._applyModification(y,arguments)},_applyCombination:function(t,e){return t.apply(null,[this].concat([].slice.call(e)))},analogous:function(){return this._applyCombination(C,arguments)},complement:function(){return this._applyCombination(w,arguments)},monochromatic:function(){return this._applyCombination(M,arguments)},splitcomplement:function(){return this._applyCombination(x,arguments)},triad:function(){return this._applyCombination(_,arguments)},tetrad:function(){return this._applyCombination(k,arguments)}},l.fromRatio=function(t,e){if("object"==typeof t){var a={};for(var r in t)t.hasOwnProperty(r)&&(a[r]="a"===r?t[r]:Y(t[r]));t=a}return l(t,e)},l.equals=function(t,e){return!(!t||!e)&&l(t).toRgbString()==l(e).toRgbString()},l.random=function(){return l.fromRatio({r:o(),g:o(),b:o()})},l.mix=function(t,e,a){a=0===a?0:a||50;var r,n=l(t).toRgb(),i=l(e).toRgb(),s=a/100,o=2*s-1,c=i.a-n.a,u=1-(r=((r=o*c==-1?o:(o+c)/(1+o*c))+1)/2),f={r:i.r*r+n.r*u,g:i.g*r+n.g*u,b:i.b*r+n.b*u,a:i.a*s+n.a*(1-s)};return l(f)},l.readability=function(t,e){var a=l(t),r=l(e),n=a.toRgb(),i=r.toRgb(),s=a.getBrightness(),o=r.getBrightness(),c=Math.max(n.r,i.r)-Math.min(n.r,i.r)+Math.max(n.g,i.g)-Math.min(n.g,i.g)+Math.max(n.b,i.b)-Math.min(n.b,i.b);return{brightness:Math.abs(s-o),color:c}},l.isReadable=function(t,e){var a=l.readability(t,e);return a.brightness>125&&a.color>500},l.mostReadable=function(t,e){for(var a=null,r=0,n=!1,i=0;i<e.length;i++){var s=l.readability(t,e[i]),o=s.brightness>125&&s.color>500,c=s.brightness/125*3+s.color/500;(o&&!n||o&&n&&c>r||!o&&!n&&c>r)&&(n=o,r=c,a=l(e[i]))}return a};var S=l.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},P=l.hexNames=function(t){var e={};for(var a in t)t.hasOwnProperty(a)&&(e[t[a]]=a);return e}(S);function A(t){return t=parseFloat(t),(isNaN(t)||t<0||t>1)&&(t=1),t}function R(t,e){var a;"string"==typeof(a=t)&&-1!=a.indexOf(".")&&1===parseFloat(a)&&(t="100%");var n,o="string"==typeof(n=t)&&-1!=n.indexOf("%");return t=i(e,s(0,parseFloat(t))),o&&(t=parseInt(t*e,10)/100),r.abs(t-e)<1e-6?1:t%e/parseFloat(e)}function H(t){return i(1,s(0,t))}function F(t){return parseInt(t,16)}function T(t){return 1==t.length?"0"+t:""+t}function Y(t){return t<=1&&(t=100*t+"%"),t}var O,B,K,I=(B="[\\s|\\(]+("+(O="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+O+")[,|\\s]+("+O+")\\s*\\)?",K="[\\s|\\(]+("+O+")[,|\\s]+("+O+")[,|\\s]+("+O+")[,|\\s]+("+O+")\\s*\\)?",{rgb:new RegExp("rgb"+B),rgba:new RegExp("rgba"+K),hsl:new RegExp("hsl"+B),hsla:new RegExp("hsla"+K),hsv:new RegExp("hsv"+B),hsva:new RegExp("hsva"+K),hex3:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex8:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});window.tinycolor=l}(),t(function(){t.fn.spectrum.load&&t.fn.spectrum.processNativeColorInputs()})});