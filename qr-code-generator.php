<?php
/**
 * Plugin Name: WC Designer Pro Custom QR Code Generator
 * Description: Custom QR code generator for WC Designer Pro using external API
 * Version: 1.0
 * Author: Custom Development
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Hook into WordPress AJAX for logged-in users
add_action('wp_ajax_wcdp_custom_qr_code', 'wcdp_handle_custom_qr_request');
add_action('wp_ajax_nopriv_wcdp_custom_qr_code', 'wcdp_handle_custom_qr_request');

function wcdp_handle_custom_qr_request() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'wcdp_qr_nonce')) {
        wp_die('Security check failed');
    }
    
    // Get parameters from request
    $text = sanitize_text_field($_POST['text'] ?? 'Default QR Text');
    $foreground_color = sanitize_hex_color($_POST['foreground_color'] ?? '#000000');
    $background_color = sanitize_hex_color($_POST['background_color'] ?? '#FFFFFF');
    $error_correction = sanitize_text_field($_POST['error_correction'] ?? 'MEDIUM');
    $logo = sanitize_text_field($_POST['logo'] ?? 'scan-me-square');
    $size = intval($_POST['size'] ?? 200);
    
    // Prepare API request
    $api_url = 'https://api.qr-code-generator.com/v1/create';
    $api_data = array(
        'frame_name' => $logo,
        'qr_code_text' => $text,
        'image_format' => 'SVG',
        'qr_code_logo' => $logo,
        'foreground_color' => $foreground_color,
        'background_color' => $background_color,
        'error_correction' => $error_correction,
        'marker_left_template' => 'version13',
        'marker_right_template' => 'version13', 
        'marker_bottom_template' => 'version13',
        'marker_left_inner_template' => 'version13',
        'marker_right_inner_template' => 'version13',
        'marker_bottom_inner_template' => 'version13',
        'pattern_template' => 'version13',
        'qr_code_pattern' => 'square',
        'size' => $size
    );
    
    // Make API request
    $response = wp_remote_post($api_url, array(
        'timeout' => 30,
        'headers' => array(
            'Content-Type' => 'application/json',
        ),
        'body' => json_encode($api_data)
    ));
    
    if (is_wp_error($response)) {
        wp_send_json_error('API request failed: ' . $response->get_error_message());
        return;
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    
    if ($response_code === 200) {
        // API returns SVG directly
        wp_send_json_success(array(
            'svg' => $response_body,
            'text' => $text,
            'logo' => $logo
        ));
    } else {
        wp_send_json_error('API returned error code: ' . $response_code);
    }
}

// Minimal test approach - add very simple script to footer
function wcdp_qr_direct_script() {
    if (isset($_GET['dp_mode']) && $_GET['dp_mode'] === 'designer') {
        $ajax_url = admin_url('admin-ajax.php');
        $nonce = wp_create_nonce('wcdp_qr_nonce');
        ?>
        <script type="text/javascript">
        console.log("QR Plugin: Minimal test script loaded");
        window.qrTestLoaded = true;
        window.testBasic = function() { 
            console.log("Basic test function works!"); 
            return true; 
        };
        </script>
        
        <script type="text/javascript">
        window.wcdp_qr_ajax = {
            "ajax_url": "<?php echo esc_js($ajax_url); ?>",
            "nonce": "<?php echo esc_js($nonce); ?>"
        };
        console.log("QR AJAX vars loaded");
        </script>
        
        <script type="text/javascript">
        window.createSimpleQR = function(text) {
            console.log("Creating simple QR for:", text);
            if (!window.wcdp_qr_ajax) {
                console.error("AJAX vars not available");
                return;
            }
            
            jQuery.post(window.wcdp_qr_ajax.ajax_url, {
                action: 'wcdp_custom_qr_code',
                nonce: window.wcdp_qr_ajax.nonce,
                text: text || 'Test QR',
                foreground_color: '#000000',
                background_color: '#FFFFFF',
                error_correction: 'MEDIUM',
                logo: 'scan-me-square',
                size: 200
            }).done(function(response) {
                console.log("QR API response:", response);
                if (response.success && response.svg) {
                    showSimpleQR(response.svg, text);
                }
            }).fail(function(xhr, status, error) {
                console.error("QR API failed:", error);
            });
        };
        
        function showSimpleQR(svg, text) {
            var existing = document.getElementById('simple-qr');
            if (existing) existing.remove();
            
            var qr = document.createElement('div');
            qr.id = 'simple-qr';
            qr.innerHTML = svg;
            qr.style.cssText = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);width:200px;height:200px;z-index:999999;border:3px solid red;background:white;padding:10px;';
            qr.title = 'QR: ' + text;
            
            document.body.appendChild(qr);
            console.log("Simple QR displayed");
        }
        
        console.log("Simple QR functions ready. Try: createSimpleQR('test')");
        </script>
        <?php
    }
}
add_action('wp_footer', 'wcdp_qr_direct_script', 999);
