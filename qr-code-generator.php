<?php
/*
Plugin Name: QR Code Generator Shortcode
Description: Generates a QR code with logo using QR Code Generator API.
Version: 1.0
Author: ChatGPT
*/

function generate_custom_qr_code($text, $options = array()) {
    $api_url = 'https://api.qr-code-generator.com/v1/create?access-token=QgC3EUE21Rtt70g-VDqiUp5vAMcTsgcbJ_7K251iHXUt9G9lyZ7-o0fbKyekEAoP';

    // Default options
    $defaults = array(
        'frame_name'     => 'no-frame',
        'image_format'   => 'SVG',
        'qr_code_logo'   => 'scan-me-square',
        'foreground_color' => '#000000',
        'background_color' => '#FFFFFF',
        'error_correction' => 'M', // L, M, Q, H
        'size' => 200
    );

    $options = wp_parse_args($options, $defaults);

    $body = array(
        'frame_name'     => $options['frame_name'],
        'qr_code_text'   => $text,
        'image_format'   => $options['image_format'],
        'qr_code_logo'   => $options['qr_code_logo'],
        'foreground_color' => $options['foreground_color'],
        'background_color' => $options['background_color'],
        'error_correction' => $options['error_correction'],
        'size' => $options['size']
    );

    $response = wp_remote_post($api_url, array(
        'headers' => array(
            'Content-Type' => 'application/json'
        ),
        'body' => wp_json_encode($body),
        'timeout' => 30
    ));

    if (is_wp_error($response)) {
        return array(
            'success' => false,
            'error' => $response->get_error_message()
        );
    }

    $response_code = wp_remote_retrieve_response_code($response);
    if ($response_code !== 200) {
        return array(
            'success' => false,
            'error' => 'API returned status code: ' . $response_code
        );
    }

    $svg = wp_remote_retrieve_body($response);

    // For shortcode usage, return wrapped HTML
    if (!isset($options['ajax_request'])) {
        return '<div class="custom-qr-wrapper">' . $svg . '</div>';
    }

    // For AJAX usage, return raw SVG
    return array(
        'success' => true,
        'svg' => $svg
    );
}

function qr_code_shortcode($atts) {
    $a = shortcode_atts(array(
        'text' => 'https://bakedbot.ai/'
    ), $atts);

    return generate_custom_qr_code($a['text']);
}
add_shortcode('generate_qr', 'qr_code_shortcode');

// Debug shortcode for testing
function qr_debug_shortcode($atts) {
    $a = shortcode_atts(array(
        'text' => 'Debug Test QR Code'
    ), $atts);

    $result = generate_custom_qr_code($a['text'], array('ajax_request' => true));

    $output = '<div style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">';
    $output .= '<h3>QR Code Debug Test</h3>';
    $output .= '<p><strong>Text:</strong> ' . esc_html($a['text']) . '</p>';

    if (is_array($result)) {
        if ($result['success']) {
            $output .= '<p style="color: green;"><strong>Status:</strong> Success</p>';
            $output .= '<div style="border: 1px solid #ddd; padding: 10px; max-width: 300px;">';
            $output .= $result['svg'];
            $output .= '</div>';
        } else {
            $output .= '<p style="color: red;"><strong>Status:</strong> Error</p>';
            $output .= '<p><strong>Error:</strong> ' . esc_html($result['error']) . '</p>';
        }
    } else {
        $output .= '<p style="color: orange;"><strong>Status:</strong> Unexpected response</p>';
        $output .= '<pre>' . esc_html(print_r($result, true)) . '</pre>';
    }

    $output .= '</div>';
    return $output;
}
add_shortcode('qr_debug', 'qr_debug_shortcode');

// AJAX handler for WCDP integration
function wcdp_custom_qr_code_ajax() {
    // Add debugging
    error_log('WCDP QR Code AJAX called with data: ' . print_r($_POST, true));

    // Verify nonce for security
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wcdp_qr_nonce')) {
        error_log('WCDP QR Code: Nonce verification failed');
        wp_send_json_error('Security check failed');
        return;
    }

    // Validate required fields
    if (!isset($_POST['text']) || empty(trim($_POST['text']))) {
        error_log('WCDP QR Code: No text provided');
        wp_send_json_error('No text provided for QR code generation');
        return;
    }

    $text = sanitize_text_field($_POST['text']);
    $foreground_color = isset($_POST['foreground_color']) ? sanitize_hex_color($_POST['foreground_color']) : '#000000';
    $background_color = isset($_POST['background_color']) ? sanitize_hex_color($_POST['background_color']) : '#FFFFFF';
    $error_correction = isset($_POST['error_correction']) ? sanitize_text_field($_POST['error_correction']) : 'MEDIUM';
    $logo = isset($_POST['logo']) ? sanitize_text_field($_POST['logo']) : 'scan-me-square';
    $size = isset($_POST['size']) ? intval($_POST['size']) : 200;

    // Map WCDP error correction levels to API format
    $error_correction_map = array(
        'LOW' => 'L',
        'MEDIUM' => 'M',
        'QUARTILE' => 'Q',
        'HIGH' => 'H'
    );

    $api_error_correction = isset($error_correction_map[$error_correction])
        ? $error_correction_map[$error_correction]
        : 'M';

    $options = array(
        'foreground_color' => $foreground_color ?: '#000000',
        'background_color' => $background_color ?: '#FFFFFF',
        'error_correction' => $api_error_correction,
        'qr_code_logo' => $logo ?: 'scan-me-square',
        'size' => $size ?: 200,
        'ajax_request' => true
    );

    error_log('WCDP QR Code: Calling generate_custom_qr_code with options: ' . print_r($options, true));

    $result = generate_custom_qr_code($text, $options);

    error_log('WCDP QR Code: API result: ' . print_r($result, true));

    if (is_array($result)) {
        wp_send_json($result);
    } else {
        wp_send_json_error('Unexpected response format from QR code generator');
    }
}

// Register AJAX handlers for both logged in and non-logged in users
add_action('wp_ajax_wcdp_custom_qr_code', 'wcdp_custom_qr_code_ajax');
add_action('wp_ajax_nopriv_wcdp_custom_qr_code', 'wcdp_custom_qr_code_ajax');

// Enqueue script to provide nonce for AJAX calls
function wcdp_qr_enqueue_scripts() {
    // Check if we're on a page that might have WCDP editor
    $should_enqueue = false;

    if (is_admin()) {
        $should_enqueue = true;
    } elseif (isset($_GET['dp_mode']) && $_GET['dp_mode'] === 'designer') {
        $should_enqueue = true;
    } elseif (function_exists('wcdp_check_mode_design_page') && wcdp_check_mode_design_page()) {
        $should_enqueue = true;
    }

    if ($should_enqueue) {
        // Enqueue to footer and make sure it's available
        wp_add_inline_script('jquery', '
            window.wcdp_qr_ajax = {
                ajax_url: "' . admin_url('admin-ajax.php') . '",
                nonce: "' . wp_create_nonce('wcdp_qr_nonce') . '"
            };
            console.log("WCDP QR AJAX variables loaded:", window.wcdp_qr_ajax);

            // Create custom QR code function and bind to button
            jQuery(document).ready(function($) {
                console.log("QR Code plugin: Document ready, setting up custom QR function");

                // Create our custom QR function
                window.wcdp_custom_qr_generate = function(ins) {
                    console.log("Custom QR function called with ins:", ins);

                    // For new QR codes, we don\'t need to check existing objects
                    var isUpdate = false;
                    if (ins === "change" && typeof wcdp_get_selection_obj === "function") {
                        var obj = wcdp_get_selection_obj();
                        isUpdate = obj.length == 1 && obj[0].clas == "qr";
                    }

                    // Get QR code parameters with better debugging
                    var textField = $("#wcdp-text-box-qr"),
                        fg = $("#wcdp-fg-qr-color"),
                        bg = $("#wcdp-bg-qr-color"),
                        levelField = $("#wcdp-qr-level"),
                        logoField = $("#wcdp-qr-logo");

                    // Get values with fallbacks
                    var qrText = textField.val() || textField.text() || "";
                    var level = levelField.val() || levelField.find("option:selected").val() || "MEDIUM";
                    var logo = logoField.length ? (logoField.val() || "scan-me-square") : "scan-me-square";

                    // If text is still empty, try to get it from the input directly
                    if (!qrText && textField.length) {
                        qrText = textField[0].value || "";
                    }

                    var params = {"text": qrText, "level": level, "logo": logo};

                    console.log("Custom QR parameters:", params);
                    console.log("QR Text field details:", {
                        value: textField.val(),
                        text: textField.text(),
                        directValue: textField.length ? textField[0].value : "no field",
                        placeholder: textField.attr("placeholder")
                    });
                    console.log("Available elements:", {
                        textField: textField.length,
                        fgColor: fg.length,
                        bgColor: bg.length,
                        levelSelect: levelField.length,
                        logoSelect: logoField.length
                    });

                    // Debug: try to set and read the text field
                    if (textField.length && !qrText) {
                        console.log("Attempting to set test text...");
                        textField.val("Debug Test QR");
                        textField.trigger("change");
                        qrText = textField.val();
                        console.log("After setting test text:", qrText);
                        params.text = qrText;
                    }

                    // Validate required text
                    if (!qrText || qrText.trim() === "") {
                        console.log("No QR text provided");
                        if (typeof wcdp_jbox_msg_modal === "function") {
                            wcdp_jbox_msg_modal("Please enter text for QR code generation");
                        } else {
                            alert("Please enter text for QR code generation");
                        }
                        return false;
                    }

                            // Show loading if available
                            if (typeof wcdp_loading !== "undefined" && wcdp_loading.show) {
                                wcdp_loading.show();
                            }

                            // Make AJAX call to custom QR code plugin
                            $.ajax({
                                url: window.wcdp_qr_ajax.ajax_url,
                                type: "POST",
                                data: {
                                    action: "wcdp_custom_qr_code",
                                    nonce: window.wcdp_qr_ajax.nonce,
                                    text: qrText,
                                    foreground_color: fg.length ? fg.spectrum("get").toHexString() : "#000000",
                                    background_color: bg.length ? bg.spectrum("get").toHexString() : "#FFFFFF",
                                    error_correction: level,
                                    logo: logo,
                                    size: 200
                                },
                                timeout: 30000,
                                success: function(response) {
                                    console.log("Custom QR AJAX success:", response);

                                    if (typeof wcdp_loading !== "undefined" && wcdp_loading.hide) {
                                        wcdp_loading.hide();
                                    }

                                    if (response.success && response.svg) {
                                        // Load the SVG from the API response
                                        if (typeof fabric !== "undefined" && fabric.loadSVGFromString) {
                                            fabric.loadSVGFromString(response.svg, function(objects, options){
                                                console.log("SVG loaded, objects:", objects.length);

                                                var newQR = fabric.util.groupSVGElements(objects, options),
                                                    size = typeof wcdp_parameters !== "undefined" && wcdp_parameters.qr_size ? parseInt(wcdp_parameters.qr_size) : 80;

                                                console.log("QR object created:", newQR);
                                                // Function to find canvas editor with retries
                                                function findCanvasEditor() {
                                                    var canvasEditor = null;
                                                    var possibleNames = ["wcdp_canvas_editor", "canvas", "fabricCanvas", "editor", "foundCanvas"];

                                                    // Try global variables first
                                                    for (var i = 0; i < possibleNames.length; i++) {
                                                        if (typeof window[possibleNames[i]] !== "undefined" && window[possibleNames[i]] && window[possibleNames[i]].add) {
                                                            canvasEditor = window[possibleNames[i]];
                                                            console.log("Found canvas editor as:", possibleNames[i]);
                                                            break;
                                                        }
                                                    }

                                                    // Try to find canvas by element ID
                                                    if (!canvasEditor) {
                                                        var canvasElement = document.getElementById("wcdp-canvas-editor");
                                                        if (canvasElement && typeof fabric !== "undefined") {
                                                            // Try to get fabric canvas from the element
                                                            if (canvasElement.__fabric) {
                                                                canvasEditor = canvasElement.__fabric;
                                                                console.log("Found canvas editor via element.__fabric");
                                                            }
                                                        }
                                                    }

                                                    // Deep search all canvas elements
                                                    if (!canvasEditor) {
                                                        var allCanvasElements = document.querySelectorAll("canvas");
                                                        for (var i = 0; i < allCanvasElements.length; i++) {
                                                            var canvas = allCanvasElements[i];
                                                            if (canvas.__fabric && canvas.__fabric.add) {
                                                                canvasEditor = canvas.__fabric;
                                                                console.log("Found canvas editor via deep search:", canvas.id || "canvas-" + i);
                                                                break;
                                                            }
                                                        }
                                                    }

                                                    return canvasEditor;
                                                }

                                                // Try to find canvas with retries
                                                function addQRToCanvasWithRetries(qrObject, maxRetries) {
                                                    maxRetries = maxRetries || 5;
                                                    var retryCount = 0;

                                                    function attemptAdd() {
                                                        var canvasEditor = findCanvasEditor();

                                                        console.log("Attempt", retryCount + 1, "- Canvas editor found:", !!canvasEditor);

                                                        if (canvasEditor && canvasEditor.add) {
                                                            try {
                                                                if (canvasEditor.discardActiveGroup) {
                                                                    canvasEditor.discardActiveGroup();
                                                                }
                                                                canvasEditor.add(qrObject);
                                                                if (canvasEditor.setActiveObject) {
                                                                    canvasEditor.setActiveObject(qrObject);
                                                                }
                                                                if (canvasEditor.renderAll) {
                                                                    canvasEditor.renderAll();
                                                                }
                                                                console.log("QR code successfully added to canvas!");
                                                                return true;
                                                            } catch (e) {
                                                                console.error("Error adding QR to canvas:", e);
                                                            }
                                                        }

                                                        retryCount++;
                                                        if (retryCount < maxRetries) {
                                                            console.log("Canvas not ready, retrying in", retryCount * 500, "ms...");
                                                            setTimeout(attemptAdd, retryCount * 500);
                                                        } else {
                                                            console.error("Failed to add QR code to canvas after", maxRetries, "attempts");
                                                            console.log("Available window objects:", Object.keys(window).filter(k => k.includes("canvas") || k.includes("editor") || k.includes("wcdp")));
                                                        }
                                                        return false;
                                                    }

                                                    return attemptAdd();
                                                }

                                                // Set CMYK values for color management if available
                                                if (newQR.paths && newQR.paths.length >= 2) {
                                                    if (bg.length && bg.attr("cmyk")) newQR.paths[0].fillCMYK = bg.attr("cmyk");
                                                    if (fg.length && fg.attr("cmyk")) newQR.paths[1].fillCMYK = fg.attr("cmyk");
                                                }

                                                if(isUpdate && typeof wcdp_get_selection_obj === "function"){
                                                    // Update existing QR code
                                                    var obj = wcdp_get_selection_obj();
                                                    if (obj.length > 0 && newQR.paths && obj[0].paths) {
                                                        obj[0].paths[0] = newQR.paths[0];
                                                        obj[0].paths[1] = newQR.paths[1];
                                                        obj[0].set({
                                                            width: newQR.width,
                                                            height: newQR.height,
                                                            dataDP: params
                                                        }).setCoords();
                                                        console.log("Updated existing QR code");

                                                        // Render the canvas
                                                        var canvasEditor = findCanvasEditor();
                                                        if (canvasEditor && canvasEditor.renderAll) {
                                                            canvasEditor.renderAll();
                                                        }
                                                    }
                                                } else{
                                                    // Add new QR code
                                                    newQR.set({
                                                        top: 50,
                                                        left: 50,
                                                        clas: "qr",
                                                        strokeWidth: 0,
                                                        dataDP: params
                                                    }).scaleToWidth(size).scaleToHeight(size * newQR.height / newQR.width);

                                                    console.log("Adding new QR code to canvas with retry mechanism");

                                                    // Use retry mechanism to add QR code
                                                    addQRToCanvasWithRetries(newQR, 10);
                                                }

                                                if (typeof wcdp_check_obj_center === "function") {
                                                    wcdp_check_obj_center(newQR);
                                                }

                                                if (typeof wcdp_save_canvas_state === "function") {
                                                    wcdp_save_canvas_state();
                                                }

                                                console.log("QR code successfully added to canvas");
                                            });
                                        } else {
                                            console.error("Fabric.js not available");
                                        }
                                    } else {
                                        // Handle API error
                                        var errorMsg = response.error || "Failed to generate QR code. Please try again.";
                                        if (typeof wcdp_jbox_msg_modal === "function") {
                                            wcdp_jbox_msg_modal("QR Code Error: " + errorMsg);
                                        } else {
                                            alert("QR Code Error: " + errorMsg);
                                        }
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error("Custom QR AJAX Error:", {xhr: xhr, status: status, error: error});

                                    if (typeof wcdp_loading !== "undefined" && wcdp_loading.hide) {
                                        wcdp_loading.hide();
                                    }

                                    var errorMessage = "Network error occurred while generating QR code.";
                                    if (status === "timeout") {
                                        errorMessage = "QR code generation timed out. Please try again.";
                                    } else if (xhr.status === 403) {
                                        errorMessage = "Permission denied. Please refresh the page and try again.";
                                    } else if (xhr.status === 500) {
                                        errorMessage = "Server error occurred. Please try again later.";
                                    }

                                    if (typeof wcdp_jbox_msg_modal === "function") {
                                        wcdp_jbox_msg_modal(errorMessage + " If the problem persists, please contact support.");
                                    } else {
                                        alert(errorMessage);
                                    }
                                }
                            });
                };

                console.log("Custom QR function created");

                // Function to wait for canvas initialization
                function waitForCanvasInit(callback, maxWait) {
                    maxWait = maxWait || 30000; // 30 seconds max
                    var startTime = Date.now();

                    function checkCanvas() {
                        var canvasEl = document.getElementById("wcdp-canvas-editor");
                        if (canvasEl && canvasEl.__fabric && canvasEl.__fabric.add) {
                            console.log("Canvas is now initialized!");
                            window.wcdp_canvas_editor = canvasEl.__fabric;
                            callback(canvasEl.__fabric);
                            return;
                        }

                        // Also check upper canvas
                        var allCanvases = document.querySelectorAll("canvas");
                        for (var i = 0; i < allCanvases.length; i++) {
                            if (allCanvases[i].__fabric && allCanvases[i].__fabric.add) {
                                console.log("Found initialized canvas:", allCanvases[i].className || "canvas-" + i);
                                window.wcdp_canvas_editor = allCanvases[i].__fabric;
                                callback(allCanvases[i].__fabric);
                                return;
                            }
                        }

                        if (Date.now() - startTime < maxWait) {
                            setTimeout(checkCanvas, 200);
                        } else {
                            console.log("Canvas initialization timeout after", maxWait, "ms");
                            callback(null);
                        }
                    }

                    checkCanvas();
                }

                // Bind to QR button click with multiple approaches
                function bindQRButton() {
                    var button = $("#wcdp-btn-make-qr");
                    if (button.length) {
                        console.log("Found QR button, binding click handler");

                        // Remove any existing handlers first
                        button.off("click.customqr");

                        // Add our custom handler
                        button.on("click.customqr", function(e) {
                            console.log("QR button clicked - custom handler");
                            e.preventDefault();
                            e.stopPropagation();

                            // Wait for canvas to be ready, then generate QR
                            console.log("Waiting for canvas to initialize...");
                            waitForCanvasInit(function(canvas) {
                                if (canvas) {
                                    console.log("Canvas ready, generating QR code");
                                    window.wcdp_custom_qr_generate();
                                } else {
                                    console.log("Canvas initialization failed, trying anyway");
                                    window.wcdp_custom_qr_generate();
                                }
                            });

                            return false;
                        });

                        console.log("QR button handler bound successfully");
                        return true;
                    }
                    return false;
                }

                // Try to bind immediately
                if (!bindQRButton()) {
                    console.log("QR button not found immediately, waiting...");

                    // Try again after short delays
                    setTimeout(function() {
                        if (!bindQRButton()) {
                            setTimeout(function() {
                                if (!bindQRButton()) {
                                    console.log("QR button still not found after delays");
                                }
                            }, 2000);
                        }
                    }, 500);
                }

                // Also use event delegation as fallback
                $(document).on("click", "#wcdp-btn-make-qr", function(e) {
                    console.log("Delegated QR button click handler triggered");
                    e.preventDefault();
                    e.stopPropagation();

                    // Try to use existing WCDP canvas first
                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    if (canvasEl && canvasEl.__fabric && canvasEl.__fabric.add) {
                        console.log("Using existing WCDP canvas");
                        window.wcdp_canvas_editor = canvasEl.__fabric;
                        window.wcdp_custom_qr_generate();
                    } else {
                        console.log("WCDP canvas not ready, trying to initialize...");
                        // Try to trigger the original WCDP QR function first to initialize canvas
                        if (typeof window.wcdp_make_qrcode_canvas_original === "function") {
                            console.log("Calling original WCDP QR function to initialize canvas");
                            try {
                                window.wcdp_make_qrcode_canvas_original();
                                // Wait a moment then try our function
                                setTimeout(function() {
                                    if (canvasEl && canvasEl.__fabric) {
                                        window.wcdp_canvas_editor = canvasEl.__fabric;
                                        window.wcdp_custom_qr_generate();
                                    } else {
                                        console.log("Canvas still not ready, using force create");
                                        forceCreateCanvas(true);
                                    }
                                }, 1000);
                            } catch (e) {
                                console.log("Error calling original function:", e);
                                window.wcdp_custom_qr_generate();
                            }
                        } else {
                            window.wcdp_custom_qr_generate();
                        }
                    }

                    return false;
                });

                // Add global test function
                window.testQRCodeGeneration = function(testText) {
                    testText = testText || "Test QR Code";
                    console.log("Testing QR code generation with text:", testText);

                    $.ajax({
                        url: window.wcdp_qr_ajax.ajax_url,
                        type: "POST",
                        data: {
                            action: "wcdp_custom_qr_code",
                            nonce: window.wcdp_qr_ajax.nonce,
                            text: testText,
                            foreground_color: "#000000",
                            background_color: "#FFFFFF",
                            error_correction: "MEDIUM",
                            logo: "scan-me-square",
                            size: 200
                        },
                        success: function(response) {
                            console.log("QR Code test success:", response);

                            // If successful, try to add to canvas
                            if (response.success && response.svg && typeof fabric !== "undefined") {
                                fabric.loadSVGFromString(response.svg, function(objects, options){
                                    console.log("Test: SVG loaded, objects:", objects.length);

                                    var newQR = fabric.util.groupSVGElements(objects, options);
                                    newQR.set({
                                        top: 100,
                                        left: 100,
                                        clas: "qr",
                                        strokeWidth: 0,
                                        dataDP: {text: testText, level: "MEDIUM", logo: "scan-me-square"}
                                    }).scaleToWidth(80);

                                    if (typeof wcdp_canvas_editor !== "undefined" && wcdp_canvas_editor) {
                                        wcdp_canvas_editor.add(newQR).setActiveObject(newQR);
                                        wcdp_canvas_editor.renderAll();
                                        console.log("Test QR code added to canvas successfully!");
                                    } else {
                                        console.log("Canvas editor not available for test");
                                    }
                                });
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("QR Code test error:", {xhr: xhr, status: status, error: error});
                        }
                    });
                };

                // Add function to test with form values
                window.testQRWithForm = function() {
                    console.log("Testing QR with current form values...");

                    // Set a test value first
                    $("#wcdp-text-box-qr").val("Form Test QR Code");

                    // Wait a moment then call our function
                    setTimeout(function() {
                        window.wcdp_custom_qr_generate();
                    }, 100);
                };

                // Add function to test with canvas waiting
                window.testQRWithCanvasWait = function() {
                    console.log("Testing QR with canvas initialization wait...");

                    // Set a test value first
                    $("#wcdp-text-box-qr").val("Canvas Wait Test QR Code");

                    // Wait for canvas to be ready
                    waitForCanvasInit(function(canvas) {
                        if (canvas) {
                            console.log("Canvas is ready, generating QR code");
                            window.wcdp_custom_qr_generate();
                        } else {
                            console.log("Canvas wait timeout, trying anyway");
                            window.wcdp_custom_qr_generate();
                        }
                    });
                };

                // Add function to try triggering canvas initialization
                window.triggerCanvasInit = function() {
                    console.log("Attempting to trigger canvas initialization...");

                    // Try clicking on canvas area to trigger initialization
                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    if (canvasEl) {
                        console.log("Clicking on canvas to trigger initialization");
                        canvasEl.click();
                        canvasEl.focus();

                        // Try triggering mouse events
                        var mouseEvent = new MouseEvent("mousedown", {
                            view: window,
                            bubbles: true,
                            cancelable: true,
                            clientX: 100,
                            clientY: 100
                        });
                        canvasEl.dispatchEvent(mouseEvent);

                        setTimeout(function() {
                            var mouseUpEvent = new MouseEvent("mouseup", {
                                view: window,
                                bubbles: true,
                                cancelable: true,
                                clientX: 100,
                                clientY: 100
                            });
                            canvasEl.dispatchEvent(mouseUpEvent);
                        }, 100);
                    }

                    // Try to find and call WCDP initialization functions
                    var initFunctions = ["wcdp_init_canvas", "wcdp_canvas_init", "initCanvas", "init_canvas"];
                    initFunctions.forEach(function(funcName) {
                        if (typeof window[funcName] === "function") {
                            console.log("Calling", funcName);
                            try {
                                window[funcName]();
                            } catch (e) {
                                console.log("Error calling", funcName, ":", e);
                            }
                        }
                    });

                    // Check if canvas is now initialized
                    setTimeout(function() {
                        findCanvasEditor();
                    }, 1000);
                };

                // Add function to force create a Fabric canvas while preserving content
                window.forceCreateCanvas = function(preserveContent) {
                    console.log("Force creating Fabric canvas...");

                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    if (canvasEl && typeof fabric !== "undefined") {
                        try {
                            var existingContent = null;

                            // Try to preserve existing content if requested
                            if (preserveContent !== false) {
                                // Check if there is already a canvas with content
                                if (canvasEl.__fabric && canvasEl.__fabric.getObjects) {
                                    existingContent = canvasEl.__fabric.toJSON();
                                    console.log("Preserving existing canvas content with", canvasEl.__fabric.getObjects().length, "objects");
                                } else {
                                    // Try to get canvas data as image
                                    try {
                                        var canvasData = canvasEl.toDataURL();
                                        if (canvasData && canvasData !== "data:,") {
                                            existingContent = {type: "image", data: canvasData};
                                            console.log("Preserving canvas as image data");
                                        }
                                    } catch (e) {
                                        console.log("Could not preserve canvas as image:", e);
                                    }
                                }
                            }

                            // Create a new Fabric canvas
                            var fabricCanvas = new fabric.Canvas("wcdp-canvas-editor");
                            window.wcdp_canvas_editor = fabricCanvas;
                            console.log("Fabric canvas created successfully!");

                            // Restore existing content if we have it
                            if (existingContent) {
                                if (existingContent.type === "image") {
                                    // Restore as background image
                                    fabric.Image.fromURL(existingContent.data, function(img) {
                                        fabricCanvas.setBackgroundImage(img, fabricCanvas.renderAll.bind(fabricCanvas));
                                        console.log("Restored canvas background image");
                                    });
                                } else {
                                    // Restore as JSON objects
                                    fabricCanvas.loadFromJSON(existingContent, function() {
                                        fabricCanvas.renderAll();
                                        console.log("Restored canvas objects from JSON");
                                    });
                                }
                            }

                            // Test adding a QR code
                            $("#wcdp-text-box-qr").val("Force Canvas Test");
                            setTimeout(function() {
                                window.wcdp_custom_qr_generate();
                            }, 500);

                        } catch (e) {
                            console.error("Error creating Fabric canvas:", e);
                        }
                    } else {
                        console.log("Canvas element or Fabric.js not available");
                    }
                };

                // Add function to find existing WCDP canvas more aggressively
                window.findExistingWCDPCanvas = function() {
                    console.log("Searching for existing WCDP canvas...");

                    // Check all possible global variables
                    var possibleCanvasVars = [
                        "wcdp_canvas_editor", "wcdp_canvas", "canvas_editor", "canvas",
                        "fabricCanvas", "editor", "wcdp_fabric_canvas", "wcdp_editor"
                    ];

                    for (var i = 0; i < possibleCanvasVars.length; i++) {
                        var varName = possibleCanvasVars[i];
                        if (typeof window[varName] !== "undefined" && window[varName]) {
                            var obj = window[varName];
                            console.log("Checking", varName, ":", typeof obj, obj);

                            if (obj && typeof obj === "object") {
                                // Check if it has Fabric canvas methods
                                if (obj.add && obj.renderAll && obj.getObjects) {
                                    console.log("*** FOUND EXISTING WCDP CANVAS ***", varName);
                                    console.log("Objects on canvas:", obj.getObjects().length);
                                    window.wcdp_canvas_editor = obj;
                                    return obj;
                                }

                                // Check if it contains a canvas property
                                if (obj.canvas && obj.canvas.add && obj.canvas.renderAll) {
                                    console.log("*** FOUND CANVAS IN OBJECT ***", varName + ".canvas");
                                    console.log("Objects on canvas:", obj.canvas.getObjects().length);
                                    window.wcdp_canvas_editor = obj.canvas;
                                    return obj.canvas;
                                }
                            }
                        }
                    }

                    console.log("No existing WCDP canvas found");
                    return null;
                };

                // Add function to wait for real WCDP canvas and then add QR
                window.waitForRealCanvas = function(qrText) {
                    qrText = qrText || "Real Canvas Test";
                    console.log("Waiting for real WCDP canvas to initialize...");

                    var maxWait = 30000; // 30 seconds
                    var startTime = Date.now();

                    function checkForRealCanvas() {
                        var canvasEl = document.getElementById("wcdp-canvas-editor");
                        if (canvasEl && canvasEl.__fabric && canvasEl.__fabric.add && canvasEl.__fabric.getObjects) {
                            console.log("Real WCDP canvas found!");
                            console.log("Existing objects on canvas:", canvasEl.__fabric.getObjects().length);

                            // Set the global canvas reference
                            window.wcdp_canvas_editor = canvasEl.__fabric;

                            // Set QR text and generate
                            $("#wcdp-text-box-qr").val(qrText);
                            setTimeout(function() {
                                window.wcdp_custom_qr_generate();
                            }, 100);

                            return true;
                        }

                        if (Date.now() - startTime < maxWait) {
                            setTimeout(checkForRealCanvas, 500);
                        } else {
                            console.log("Timeout waiting for real WCDP canvas");
                            // Try force create as fallback
                            console.log("Falling back to force create...");
                            forceCreateCanvas(true);
                        }
                    }

                    checkForRealCanvas();
                };

                // Add function to trigger WCDP canvas initialization
                window.triggerWCDPInit = function() {
                    console.log("Attempting to trigger WCDP canvas initialization...");

                    // Try clicking on various WCDP elements that might trigger canvas init
                    var triggers = [
                        "#wcdp-canvas-editor",
                        ".wcdp-canvas-container",
                        "#wcdp-btn-text",
                        "#wcdp-btn-image",
                        ".wcdp-tab-content"
                    ];

                    triggers.forEach(function(selector) {
                        var element = document.querySelector(selector);
                        if (element) {
                            console.log("Clicking on", selector);
                            element.click();
                            element.focus();
                        }
                    });

                    // Wait a moment then check for canvas
                    setTimeout(function() {
                        waitForRealCanvas("Triggered Canvas Test");
                    }, 1000);
                };

                // Add function to override WCDP QR function more aggressively
                window.overrideWCDPQR = function() {
                    console.log("Attempting aggressive WCDP QR function override...");

                    // Try to find the original function in various ways
                    var originalFunction = null;
                    var possibleFunctions = [
                        "wcdp_make_qrcode_canvas",
                        "wcdp_make_qr_canvas",
                        "wcdp_qr_generate",
                        "make_qrcode_canvas"
                    ];

                    // Check window object
                    possibleFunctions.forEach(function(funcName) {
                        if (typeof window[funcName] === "function") {
                            console.log("Found function:", funcName);
                            originalFunction = window[funcName];
                            window[funcName + "_original"] = originalFunction;

                            // Override with our function
                            window[funcName] = function(ins) {
                                console.log("Overridden", funcName, "called with:", ins);

                                // Try to get canvas from the original function context
                                var canvasEl = document.getElementById("wcdp-canvas-editor");
                                if (canvasEl && canvasEl.__fabric) {
                                    console.log("Using existing canvas from override");
                                    window.wcdp_canvas_editor = canvasEl.__fabric;
                                    window.wcdp_custom_qr_generate(ins);
                                } else {
                                    console.log("Calling original function first to initialize canvas");
                                    try {
                                        originalFunction.call(this, ins);
                                        // Wait and try our function
                                        setTimeout(function() {
                                            if (canvasEl && canvasEl.__fabric) {
                                                window.wcdp_canvas_editor = canvasEl.__fabric;
                                                window.wcdp_custom_qr_generate(ins);
                                            }
                                        }, 500);
                                    } catch (e) {
                                        console.log("Error calling original, using our function:", e);
                                        window.wcdp_custom_qr_generate(ins);
                                    }
                                }
                            };

                            console.log("Successfully overrode", funcName);
                        }
                    });

                    if (!originalFunction) {
                        console.log("No WCDP QR function found to override");
                    }

                    return !!originalFunction;
                };

                // Add function to discover canvas objects
                window.findCanvasEditor = function() {
                    console.log("=== Canvas Editor Discovery ===");

                    // Check common variable names
                    var candidates = ["wcdp_canvas_editor", "canvas", "fabricCanvas", "editor", "canvasEditor"];
                    candidates.forEach(function(name) {
                        if (typeof window[name] !== "undefined") {
                            console.log(name + ":", typeof window[name], window[name]);
                            if (window[name] && typeof window[name] === "object") {
                                console.log("  - has add method:", typeof window[name].add === "function");
                                console.log("  - has renderAll method:", typeof window[name].renderAll === "function");
                            }
                        }
                    });

                    // Check canvas element
                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    console.log("Canvas element found:", !!canvasEl);
                    if (canvasEl) {
                        console.log("Canvas element __fabric:", !!canvasEl.__fabric);
                        if (canvasEl.__fabric) {
                            console.log("Fabric canvas methods:", {
                                add: typeof canvasEl.__fabric.add,
                                renderAll: typeof canvasEl.__fabric.renderAll
                            });
                        }
                    }

                    // Check all window objects containing "canvas" or "wcdp"
                    var relevantObjects = Object.keys(window).filter(function(key) {
                        return key.toLowerCase().includes("canvas") ||
                               key.toLowerCase().includes("wcdp") ||
                               key.toLowerCase().includes("editor");
                    });
                    console.log("Relevant window objects:", relevantObjects);

                    // Deep search for Fabric canvas instances
                    console.log("=== Deep Canvas Search ===");
                    var allCanvasElements = document.querySelectorAll("canvas");
                    console.log("Found", allCanvasElements.length, "canvas elements");

                    for (var i = 0; i < allCanvasElements.length; i++) {
                        var canvas = allCanvasElements[i];
                        console.log("Canvas", i + ":", {
                            id: canvas.id,
                            className: canvas.className,
                            hasFabric: !!canvas.__fabric,
                            fabricMethods: canvas.__fabric ? {
                                add: typeof canvas.__fabric.add,
                                renderAll: typeof canvas.__fabric.renderAll
                            } : null
                        });

                        if (canvas.__fabric && canvas.__fabric.add) {
                            console.log("*** FOUND WORKING FABRIC CANVAS ***", canvas.id || "no-id");
                            window.foundCanvas = canvas.__fabric;
                        }
                    }

                    return relevantObjects;
                };

                console.log("QR Code test function available: testQRCodeGeneration()");

                // Monitor for WCDP canvas initialization
                var canvasCheckInterval = setInterval(function() {
                    var canvasEl = document.getElementById("wcdp-canvas-editor");
                    if (canvasEl && canvasEl.__fabric && canvasEl.__fabric.add) {
                        console.log("WCDP canvas is now initialized!");
                        window.wcdp_canvas_editor = canvasEl.__fabric;
                        clearInterval(canvasCheckInterval);

                        // Log canvas info
                        console.log("Canvas objects:", canvasEl.__fabric.getObjects().length);
                        console.log("Canvas size:", canvasEl.__fabric.width, "x", canvasEl.__fabric.height);
                    }
                }, 1000);

                // Clear the interval after 60 seconds to avoid infinite checking
                setTimeout(function() {
                    clearInterval(canvasCheckInterval);
                }, 60000);
            });
        ');
    }
}
add_action('wp_enqueue_scripts', 'wcdp_qr_enqueue_scripts');
add_action('admin_enqueue_scripts', 'wcdp_qr_enqueue_scripts');

// Admin notice if WCDP is not active
function wcdp_qr_admin_notice() {
    if (!function_exists('wcdp_check_mode_design_page')) {
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>QR Code Generator:</strong> This plugin requires WooCommerce Designer Pro to be active for full functionality.</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'wcdp_qr_admin_notice');

// Check if API is accessible (for debugging)
function wcdp_qr_test_api_connection() {
    if (!current_user_can('manage_options')) {
        return;
    }

    $test_result = generate_custom_qr_code('Test QR Code', array('ajax_request' => true));

    if (isset($_GET['test_qr_api']) && $_GET['test_qr_api'] === '1') {
        if ($test_result['success']) {
            wp_die('QR Code API is working correctly!');
        } else {
            wp_die('QR Code API Error: ' . $test_result['error']);
        }
    }
}
add_action('admin_init', 'wcdp_qr_test_api_connection');
