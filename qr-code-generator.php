<?php
/*
Plugin Name: QR Code Generator Shortcode
Description: Generates a QR code with logo using QR Code Generator API.
Version: 1.0
Author: ChatGPT
*/

function generate_custom_qr_code($text) {
    $api_url = 'https://api.qr-code-generator.com/v1/create?access-token=unXHytW3is8Glt-Hdu5bQ4MPDLZ5gJ-RqIXXCkaSBpf26VLaDDLralndnz1ES0-g';

    $body = array(
        'frame_name'     => 'no-frame',
        'qr_code_text'   => $text,
        'image_format'   => 'SVG',
        'qr_code_logo'   => 'scan-me-square'
    );

    $response = wp_remote_post($api_url, array(
        'headers' => array(
            'Content-Type' => 'application/json'
        ),
        'body' => wp_json_encode($body)
    ));

    if (is_wp_error($response)) {
        return '<p style="color:red;">Error: ' . esc_html($response->get_error_message()) . '</p>';
    }

    $svg = wp_remote_retrieve_body($response);
    return '<div class="custom-qr-wrapper">' . $svg . '</div>';
}

function qr_code_shortcode($atts) {
    $a = shortcode_atts(array(
        'text' => 'https://bakedbot.ai/'
    ), $atts);

    return generate_custom_qr_code($a['text']);
}
add_shortcode('generate_qr', 'qr_code_shortcode');
