<?php
/*
Plugin Name: QR Code Generator Shortcode
Description: Generates a QR code with logo using QR Code Generator API.
Version: 1.0
Author: ChatGPT
*/

function generate_custom_qr_code($text, $options = array()) {
    $api_url = 'https://api.qr-code-generator.com/v1/create?access-token=unXHytW3is8Glt-Hdu5bQ4MPDLZ5gJ-RqIXXCkaSBpf26VLaDDLralndnz1ES0-g';

    // Default options
    $defaults = array(
        'frame_name'     => 'no-frame',
        'image_format'   => 'SVG',
        'qr_code_logo'   => 'scan-me-square',
        'foreground_color' => '#000000',
        'background_color' => '#FFFFFF',
        'error_correction' => 'M', // L, M, Q, H
        'size' => 200
    );

    $options = wp_parse_args($options, $defaults);

    $body = array(
        'frame_name'     => $options['frame_name'],
        'qr_code_text'   => $text,
        'image_format'   => $options['image_format'],
        'qr_code_logo'   => $options['qr_code_logo'],
        'foreground_color' => $options['foreground_color'],
        'background_color' => $options['background_color'],
        'error_correction' => $options['error_correction'],
        'size' => $options['size']
    );

    $response = wp_remote_post($api_url, array(
        'headers' => array(
            'Content-Type' => 'application/json'
        ),
        'body' => wp_json_encode($body),
        'timeout' => 30
    ));

    if (is_wp_error($response)) {
        return array(
            'success' => false,
            'error' => $response->get_error_message()
        );
    }

    $response_code = wp_remote_retrieve_response_code($response);
    if ($response_code !== 200) {
        return array(
            'success' => false,
            'error' => 'API returned status code: ' . $response_code
        );
    }

    $svg = wp_remote_retrieve_body($response);

    // For shortcode usage, return wrapped HTML
    if (!isset($options['ajax_request'])) {
        return '<div class="custom-qr-wrapper">' . $svg . '</div>';
    }

    // For AJAX usage, return raw SVG
    return array(
        'success' => true,
        'svg' => $svg
    );
}

function qr_code_shortcode($atts) {
    $a = shortcode_atts(array(
        'text' => 'https://bakedbot.ai/'
    ), $atts);

    return generate_custom_qr_code($a['text']);
}
add_shortcode('generate_qr', 'qr_code_shortcode');

// Debug shortcode for testing
function qr_debug_shortcode($atts) {
    $a = shortcode_atts(array(
        'text' => 'Debug Test QR Code'
    ), $atts);

    $result = generate_custom_qr_code($a['text'], array('ajax_request' => true));

    $output = '<div style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">';
    $output .= '<h3>QR Code Debug Test</h3>';
    $output .= '<p><strong>Text:</strong> ' . esc_html($a['text']) . '</p>';

    if (is_array($result)) {
        if ($result['success']) {
            $output .= '<p style="color: green;"><strong>Status:</strong> Success</p>';
            $output .= '<div style="border: 1px solid #ddd; padding: 10px; max-width: 300px;">';
            $output .= $result['svg'];
            $output .= '</div>';
        } else {
            $output .= '<p style="color: red;"><strong>Status:</strong> Error</p>';
            $output .= '<p><strong>Error:</strong> ' . esc_html($result['error']) . '</p>';
        }
    } else {
        $output .= '<p style="color: orange;"><strong>Status:</strong> Unexpected response</p>';
        $output .= '<pre>' . esc_html(print_r($result, true)) . '</pre>';
    }

    $output .= '</div>';
    return $output;
}
add_shortcode('qr_debug', 'qr_debug_shortcode');

// AJAX handler for WCDP integration
function wcdp_custom_qr_code_ajax() {
    // Add debugging
    error_log('WCDP QR Code AJAX called with data: ' . print_r($_POST, true));

    // Verify nonce for security
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wcdp_qr_nonce')) {
        error_log('WCDP QR Code: Nonce verification failed');
        wp_send_json_error('Security check failed');
        return;
    }

    // Validate required fields
    if (!isset($_POST['text']) || empty(trim($_POST['text']))) {
        error_log('WCDP QR Code: No text provided');
        wp_send_json_error('No text provided for QR code generation');
        return;
    }

    $text = sanitize_text_field($_POST['text']);
    $foreground_color = isset($_POST['foreground_color']) ? sanitize_hex_color($_POST['foreground_color']) : '#000000';
    $background_color = isset($_POST['background_color']) ? sanitize_hex_color($_POST['background_color']) : '#FFFFFF';
    $error_correction = isset($_POST['error_correction']) ? sanitize_text_field($_POST['error_correction']) : 'MEDIUM';
    $logo = isset($_POST['logo']) ? sanitize_text_field($_POST['logo']) : 'scan-me-square';
    $size = isset($_POST['size']) ? intval($_POST['size']) : 200;

    // Map WCDP error correction levels to API format
    $error_correction_map = array(
        'LOW' => 'L',
        'MEDIUM' => 'M',
        'QUARTILE' => 'Q',
        'HIGH' => 'H'
    );

    $api_error_correction = isset($error_correction_map[$error_correction])
        ? $error_correction_map[$error_correction]
        : 'M';

    $options = array(
        'foreground_color' => $foreground_color ?: '#000000',
        'background_color' => $background_color ?: '#FFFFFF',
        'error_correction' => $api_error_correction,
        'qr_code_logo' => $logo ?: 'scan-me-square',
        'size' => $size ?: 200,
        'ajax_request' => true
    );

    error_log('WCDP QR Code: Calling generate_custom_qr_code with options: ' . print_r($options, true));

    $result = generate_custom_qr_code($text, $options);

    error_log('WCDP QR Code: API result: ' . print_r($result, true));

    if (is_array($result)) {
        wp_send_json($result);
    } else {
        wp_send_json_error('Unexpected response format from QR code generator');
    }
}

// Register AJAX handlers for both logged in and non-logged in users
add_action('wp_ajax_wcdp_custom_qr_code', 'wcdp_custom_qr_code_ajax');
add_action('wp_ajax_nopriv_wcdp_custom_qr_code', 'wcdp_custom_qr_code_ajax');

// Enqueue script to provide nonce for AJAX calls
function wcdp_qr_enqueue_scripts() {
    // Check if we're on a page that might have WCDP editor
    $should_enqueue = false;

    if (is_admin()) {
        $should_enqueue = true;
    } elseif (isset($_GET['dp_mode']) && $_GET['dp_mode'] === 'designer') {
        $should_enqueue = true;
    } elseif (function_exists('wcdp_check_mode_design_page') && wcdp_check_mode_design_page()) {
        $should_enqueue = true;
    }

    if ($should_enqueue) {
        // Enqueue to footer and make sure it's available
        wp_add_inline_script('jquery', '
            window.wcdp_qr_ajax = {
                ajax_url: "' . admin_url('admin-ajax.php') . '",
                nonce: "' . wp_create_nonce('wcdp_qr_nonce') . '"
            };
            console.log("WCDP QR AJAX variables loaded:", window.wcdp_qr_ajax);

            // Add debugging for QR code button clicks
            jQuery(document).ready(function($) {
                console.log("QR Code plugin: Document ready, looking for WCDP QR button");

                // Check if WCDP QR button exists
                if ($("#wcdp-btn-make-qr").length) {
                    console.log("Found WCDP QR button, adding click handler");
                    $("#wcdp-btn-make-qr").on("click", function() {
                        console.log("QR Code button clicked!");
                    });
                } else {
                    console.log("WCDP QR button not found on page load");
                }

                // Monitor for dynamically added QR button
                setTimeout(function() {
                    if ($("#wcdp-btn-make-qr").length) {
                        console.log("QR button found after delay");
                    } else {
                        console.log("QR button still not found after delay");
                    }
                }, 2000);

                // Add global test function
                window.testQRCodeGeneration = function(testText) {
                    testText = testText || "Test QR Code";
                    console.log("Testing QR code generation with text:", testText);

                    $.ajax({
                        url: window.wcdp_qr_ajax.ajax_url,
                        type: "POST",
                        data: {
                            action: "wcdp_custom_qr_code",
                            nonce: window.wcdp_qr_ajax.nonce,
                            text: testText,
                            foreground_color: "#000000",
                            background_color: "#FFFFFF",
                            error_correction: "MEDIUM",
                            logo: "scan-me-square",
                            size: 200
                        },
                        success: function(response) {
                            console.log("QR Code test success:", response);
                        },
                        error: function(xhr, status, error) {
                            console.error("QR Code test error:", {xhr: xhr, status: status, error: error});
                        }
                    });
                };

                console.log("QR Code test function available: testQRCodeGeneration()");
            });
        ');
    }
}
add_action('wp_enqueue_scripts', 'wcdp_qr_enqueue_scripts');
add_action('admin_enqueue_scripts', 'wcdp_qr_enqueue_scripts');

// Admin notice if WCDP is not active
function wcdp_qr_admin_notice() {
    if (!function_exists('wcdp_check_mode_design_page')) {
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>QR Code Generator:</strong> This plugin requires WooCommerce Designer Pro to be active for full functionality.</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'wcdp_qr_admin_notice');

// Check if API is accessible (for debugging)
function wcdp_qr_test_api_connection() {
    if (!current_user_can('manage_options')) {
        return;
    }

    $test_result = generate_custom_qr_code('Test QR Code', array('ajax_request' => true));

    if (isset($_GET['test_qr_api']) && $_GET['test_qr_api'] === '1') {
        if ($test_result['success']) {
            wp_die('QR Code API is working correctly!');
        } else {
            wp_die('QR Code API Error: ' . $test_result['error']);
        }
    }
}
add_action('admin_init', 'wcdp_qr_test_api_connection');
